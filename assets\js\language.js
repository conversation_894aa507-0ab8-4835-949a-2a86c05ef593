// Language Management System

class LanguageManager {
    constructor() {
        this.currentLanguage = localStorage.getItem('language') || 'en';
        this.translations = {
            en: {
                // Navigation
                home: "Home",
                products: "Products",
                services: "Services",
                about: "About",
                contact: "Contact",
                account: "Account",
                login: "Login",
                register: "Register",
                logout: "Logout",
                myProfile: "My Profile",
                myOrders: "My Orders",
                cart: "Cart",
                
                // Homepage
                welcomeTitle: "Welcome to ModernShop",
                welcomeSubtitle: "Discover premium products and services with exceptional quality and modern design.",
                shopNow: "Shop Now",
                ourServices: "Our Services",
                whyChooseUs: "Why Choose Us?",
                whyChooseUsSubtitle: "We provide exceptional service and quality products",
                
                // Features
                fastShipping: "Fast Shipping",
                fastShippingDesc: "Quick and reliable delivery worldwide",
                securePayment: "Secure Payment",
                securePaymentDesc: "100% secure payment processing",
                support247: "24/7 Support",
                support247Desc: "Round-the-clock customer support",
                
                // Products
                featuredProducts: "Featured Products",
                featuredProductsDesc: "Discover our best-selling products",
                viewAllProducts: "View All Products",
                addToCart: "Add to Cart",
                buyNow: "Buy Now",
                outOfStock: "Out of Stock",
                inStock: "In Stock",
                price: "Price",
                rating: "Rating",
                reviews: "Reviews",
                
                // Cart
                cartEmpty: "Your cart is empty",
                cartTotal: "Total",
                checkout: "Checkout",
                continueShopping: "Continue Shopping",
                removeItem: "Remove Item",
                updateQuantity: "Update Quantity",
                
                // User Account
                firstName: "First Name",
                lastName: "Last Name",
                email: "Email",
                password: "Password",
                confirmPassword: "Confirm Password",
                phone: "Phone",
                address: "Address",
                city: "City",
                country: "Country",
                
                // Orders
                orderHistory: "Order History",
                orderNumber: "Order Number",
                orderDate: "Order Date",
                orderStatus: "Order Status",
                orderTotal: "Order Total",
                pending: "Pending",
                processing: "Processing",
                shipped: "Shipped",
                delivered: "Delivered",
                cancelled: "Cancelled",
                
                // Footer
                quickLinks: "Quick Links",
                aboutUs: "About Us",
                faq: "FAQ",
                privacyPolicy: "Privacy Policy",
                termsOfService: "Terms of Service",
                contactInfo: "Contact Info",
                newsletter: "Newsletter",
                newsletterDesc: "Subscribe to get updates on new products and offers.",
                subscribe: "Subscribe",
                enterEmail: "Enter email",
                copyright: "© 2024 ModernShop. All rights reserved.",
                adminPanel: "Admin Panel",
                
                // Messages
                productAddedToCart: "Product added to cart",
                productRemovedFromCart: "Product removed from cart",
                loginSuccessful: "Login successful",
                loginFailed: "Login failed",
                registrationSuccessful: "Registration successful",
                registrationFailed: "Registration failed",
                orderPlaced: "Order placed successfully",
                orderFailed: "Failed to place order",
                
                // Admin
                dashboard: "Dashboard",
                manageProducts: "Manage Products",
                manageOrders: "Manage Orders",
                manageUsers: "Manage Users",
                analytics: "Analytics",
                settings: "Settings",
                totalSales: "Total Sales",
                totalOrders: "Total Orders",
                totalCustomers: "Total Customers",
                totalProducts: "Total Products"
            },
            ar: {
                // Navigation
                home: "الرئيسية",
                products: "المنتجات",
                services: "الخدمات",
                about: "من نحن",
                contact: "اتصل بنا",
                account: "الحساب",
                login: "تسجيل الدخول",
                register: "إنشاء حساب",
                logout: "تسجيل الخروج",
                myProfile: "ملفي الشخصي",
                myOrders: "طلباتي",
                cart: "السلة",
                
                // Homepage
                welcomeTitle: "مرحباً بك في متجر حديث",
                welcomeSubtitle: "اكتشف المنتجات والخدمات المميزة بجودة استثنائية وتصميم عصري.",
                shopNow: "تسوق الآن",
                ourServices: "خدماتنا",
                whyChooseUs: "لماذا تختارنا؟",
                whyChooseUsSubtitle: "نقدم خدمة استثنائية ومنتجات عالية الجودة",
                
                // Features
                fastShipping: "شحن سريع",
                fastShippingDesc: "توصيل سريع وموثوق في جميع أنحاء العالم",
                securePayment: "دفع آمن",
                securePaymentDesc: "معالجة دفع آمنة بنسبة 100%",
                support247: "دعم 24/7",
                support247Desc: "دعم العملاء على مدار الساعة",
                
                // Products
                featuredProducts: "المنتجات المميزة",
                featuredProductsDesc: "اكتشف منتجاتنا الأكثر مبيعاً",
                viewAllProducts: "عرض جميع المنتجات",
                addToCart: "أضف للسلة",
                buyNow: "اشتري الآن",
                outOfStock: "غير متوفر",
                inStock: "متوفر",
                price: "السعر",
                rating: "التقييم",
                reviews: "المراجعات",
                
                // Cart
                cartEmpty: "سلتك فارغة",
                cartTotal: "المجموع",
                checkout: "الدفع",
                continueShopping: "متابعة التسوق",
                removeItem: "إزالة العنصر",
                updateQuantity: "تحديث الكمية",
                
                // User Account
                firstName: "الاسم الأول",
                lastName: "اسم العائلة",
                email: "البريد الإلكتروني",
                password: "كلمة المرور",
                confirmPassword: "تأكيد كلمة المرور",
                phone: "الهاتف",
                address: "العنوان",
                city: "المدينة",
                country: "البلد",
                
                // Orders
                orderHistory: "تاريخ الطلبات",
                orderNumber: "رقم الطلب",
                orderDate: "تاريخ الطلب",
                orderStatus: "حالة الطلب",
                orderTotal: "مجموع الطلب",
                pending: "قيد الانتظار",
                processing: "قيد المعالجة",
                shipped: "تم الشحن",
                delivered: "تم التسليم",
                cancelled: "ملغي",
                
                // Footer
                quickLinks: "روابط سريعة",
                aboutUs: "من نحن",
                faq: "الأسئلة الشائعة",
                privacyPolicy: "سياسة الخصوصية",
                termsOfService: "شروط الخدمة",
                contactInfo: "معلومات الاتصال",
                newsletter: "النشرة الإخبارية",
                newsletterDesc: "اشترك للحصول على تحديثات حول المنتجات والعروض الجديدة.",
                subscribe: "اشترك",
                enterEmail: "أدخل البريد الإلكتروني",
                copyright: "© 2024 متجر حديث. جميع الحقوق محفوظة.",
                adminPanel: "لوحة الإدارة",
                
                // Messages
                productAddedToCart: "تم إضافة المنتج إلى السلة",
                productRemovedFromCart: "تم إزالة المنتج من السلة",
                loginSuccessful: "تم تسجيل الدخول بنجاح",
                loginFailed: "فشل تسجيل الدخول",
                registrationSuccessful: "تم التسجيل بنجاح",
                registrationFailed: "فشل التسجيل",
                orderPlaced: "تم تقديم الطلب بنجاح",
                orderFailed: "فشل في تقديم الطلب",
                
                // Admin
                dashboard: "لوحة التحكم",
                manageProducts: "إدارة المنتجات",
                manageOrders: "إدارة الطلبات",
                manageUsers: "إدارة المستخدمين",
                analytics: "التحليلات",
                settings: "الإعدادات",
                totalSales: "إجمالي المبيعات",
                totalOrders: "إجمالي الطلبات",
                totalCustomers: "إجمالي العملاء",
                totalProducts: "إجمالي المنتجات"
            }
        };
        
        this.init();
    }
    
    init() {
        this.setLanguage(this.currentLanguage);
        this.setupLanguageSwitcher();
    }
    
    setLanguage(lang) {
        this.currentLanguage = lang;
        localStorage.setItem('language', lang);
        
        const html = document.documentElement;
        const body = document.body;
        
        if (lang === 'ar') {
            html.setAttribute('lang', 'ar');
            html.setAttribute('dir', 'rtl');
            body.setAttribute('dir', 'rtl');
            body.classList.add('rtl');
        } else {
            html.setAttribute('lang', 'en');
            html.setAttribute('dir', 'ltr');
            body.setAttribute('dir', 'ltr');
            body.classList.remove('rtl');
        }
        
        this.updateContent();
        this.updateLanguageSwitcher();
    }
    
    updateContent() {
        // Update elements with data attributes
        const elements = document.querySelectorAll('[data-en][data-ar]');
        elements.forEach(element => {
            const text = element.getAttribute(`data-${this.currentLanguage}`);
            if (text) {
                element.textContent = text;
            }
        });
        
        // Update placeholders
        const placeholderElements = document.querySelectorAll('[data-en-placeholder][data-ar-placeholder]');
        placeholderElements.forEach(element => {
            const placeholder = element.getAttribute(`data-${this.currentLanguage}-placeholder`);
            if (placeholder) {
                element.setAttribute('placeholder', placeholder);
            }
        });
        
        // Update elements with translation keys
        const translationElements = document.querySelectorAll('[data-translate]');
        translationElements.forEach(element => {
            const key = element.getAttribute('data-translate');
            const translation = this.getTranslation(key);
            if (translation) {
                element.textContent = translation;
            }
        });
    }
    
    updateLanguageSwitcher() {
        const langText = document.getElementById('lang-text');
        if (langText) {
            langText.textContent = this.currentLanguage === 'en' ? 'العربية' : 'English';
        }
    }
    
    setupLanguageSwitcher() {
        // Language switcher is handled by the global toggleLanguage function
        // This method can be used for additional setup if needed
    }
    
    getTranslation(key) {
        return this.translations[this.currentLanguage][key] || this.translations.en[key] || key;
    }
    
    getCurrentLanguage() {
        return this.currentLanguage;
    }
    
    isRTL() {
        return this.currentLanguage === 'ar';
    }
    
    formatNumber(number) {
        if (this.currentLanguage === 'ar') {
            // Convert to Arabic-Indic numerals
            const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
            return number.toString().replace(/\d/g, (digit) => arabicNumerals[parseInt(digit)]);
        }
        return number.toString();
    }
    
    formatCurrency(amount) {
        const formatted = new Intl.NumberFormat(this.currentLanguage === 'ar' ? 'ar-SA' : 'en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
        
        return formatted;
    }
    
    formatDate(date) {
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        
        return new Intl.DateTimeFormat(this.currentLanguage === 'ar' ? 'ar-SA' : 'en-US', options).format(new Date(date));
    }
    
    // Dynamic content translation
    translateElement(element, key) {
        const translation = this.getTranslation(key);
        if (translation) {
            element.textContent = translation;
        }
    }
    
    // Add translation to existing elements
    addTranslation(selector, key) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.setAttribute('data-translate', key);
            this.translateElement(element, key);
        });
    }
    
    // Create translated content
    createTranslatedElement(tag, key, className = '') {
        const element = document.createElement(tag);
        if (className) {
            element.className = className;
        }
        element.setAttribute('data-translate', key);
        this.translateElement(element, key);
        return element;
    }
}

// Initialize language manager
const languageManager = new LanguageManager();

// Global language functions
function toggleLanguage() {
    const newLang = languageManager.getCurrentLanguage() === 'en' ? 'ar' : 'en';
    languageManager.setLanguage(newLang);
    
    // Trigger custom event for other components to listen to
    const event = new CustomEvent('languageChanged', {
        detail: { language: newLang }
    });
    document.dispatchEvent(event);
}

function getCurrentLanguage() {
    return languageManager.getCurrentLanguage();
}

function getTranslation(key) {
    return languageManager.getTranslation(key);
}

function isRTL() {
    return languageManager.isRTL();
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LanguageManager;
}

// Demo Data Generator for ModernShop

class DemoDataGenerator {
    constructor() {
        this.sampleProducts = [
            {
                id: 1,
                name: { en: "Premium Laptop", ar: "لابتوب مميز" },
                description: { 
                    en: "High-performance laptop with Intel i7 processor, 16GB RAM, and 512GB SSD. Perfect for professionals and gamers.", 
                    ar: "لا<PERSON>توب عالي الأداء مع معالج Intel i7 وذاكرة 16 جيجابايت وقرص SSD 512 جيجابايت. مثالي للمحترفين واللاعبين." 
                },
                price: 1299.99,
                originalPrice: 1499.99,
                image: "https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop",
                category: "electronics",
                rating: 4.8,
                reviews: 124,
                inStock: true,
                stockQuantity: 15,
                featured: true,
                tags: ["laptop", "computer", "gaming", "professional"]
            },
            {
                id: 2,
                name: { en: "Wireless Headphones", ar: "سماعات لاسلكية" },
                description: { 
                    en: "Premium wireless headphones with active noise cancellation, 30-hour battery life, and superior sound quality.", 
                    ar: "سماعات لاسلكية مميزة مع إلغاء الضوضاء النشط وبطارية تدوم 30 ساعة وجودة صوت فائقة." 
                },
                price: 299.99,
                originalPrice: 399.99,
                image: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop",
                category: "electronics",
                rating: 4.6,
                reviews: 89,
                inStock: true,
                stockQuantity: 25,
                featured: true,
                tags: ["headphones", "wireless", "audio", "music"]
            },
            {
                id: 3,
                name: { en: "Smart Watch", ar: "ساعة ذكية" },
                description: { 
                    en: "Advanced smartwatch with health monitoring, GPS tracking, and 7-day battery life. Compatible with iOS and Android.", 
                    ar: "ساعة ذكية متقدمة مع مراقبة الصحة وتتبع GPS وبطارية تدوم 7 أيام. متوافقة مع iOS و Android." 
                },
                price: 399.99,
                originalPrice: 499.99,
                image: "https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=300&fit=crop",
                category: "electronics",
                rating: 4.7,
                reviews: 156,
                inStock: true,
                stockQuantity: 30,
                featured: true,
                tags: ["smartwatch", "fitness", "health", "wearable"]
            },
            {
                id: 4,
                name: { en: "Professional Camera", ar: "كاميرا احترافية" },
                description: { 
                    en: "High-resolution DSLR camera with 24MP sensor, 4K video recording, and professional lens kit.", 
                    ar: "كاميرا DSLR عالية الدقة مع مستشعر 24 ميجابكسل وتسجيل فيديو 4K ومجموعة عدسات احترافية." 
                },
                price: 899.99,
                originalPrice: 1199.99,
                image: "https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=400&h=300&fit=crop",
                category: "electronics",
                rating: 4.9,
                reviews: 78,
                inStock: true,
                stockQuantity: 8,
                featured: true,
                tags: ["camera", "photography", "professional", "DSLR"]
            },
            {
                id: 5,
                name: { en: "Gaming Chair", ar: "كرسي ألعاب" },
                description: { 
                    en: "Ergonomic gaming chair with lumbar support, adjustable height, and premium leather upholstery.", 
                    ar: "كرسي ألعاب مريح مع دعم أسفل الظهر وارتفاع قابل للتعديل وتنجيد جلدي مميز." 
                },
                price: 249.99,
                originalPrice: 349.99,
                image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop",
                category: "furniture",
                rating: 4.5,
                reviews: 92,
                inStock: true,
                stockQuantity: 12,
                featured: false,
                tags: ["chair", "gaming", "ergonomic", "furniture"]
            },
            {
                id: 6,
                name: { en: "Fitness Tracker", ar: "متتبع اللياقة" },
                description: { 
                    en: "Lightweight fitness tracker with heart rate monitoring, sleep tracking, and 10-day battery life.", 
                    ar: "متتبع لياقة خفيف الوزن مع مراقبة معدل ضربات القلب وتتبع النوم وبطارية تدوم 10 أيام." 
                },
                price: 79.99,
                originalPrice: 99.99,
                image: "https://images.unsplash.com/photo-1575311373937-040b8e1fd5b6?w=400&h=300&fit=crop",
                category: "sports",
                rating: 4.3,
                reviews: 203,
                inStock: true,
                stockQuantity: 45,
                featured: false,
                tags: ["fitness", "tracker", "health", "sports"]
            },
            {
                id: 7,
                name: { en: "Bluetooth Speaker", ar: "مكبر صوت بلوتوث" },
                description: { 
                    en: "Portable Bluetooth speaker with 360-degree sound, waterproof design, and 12-hour battery life.", 
                    ar: "مكبر صوت بلوتوث محمول مع صوت 360 درجة وتصميم مقاوم للماء وبطارية تدوم 12 ساعة." 
                },
                price: 89.99,
                originalPrice: 129.99,
                image: "https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=400&h=300&fit=crop",
                category: "electronics",
                rating: 4.4,
                reviews: 167,
                inStock: true,
                stockQuantity: 22,
                featured: false,
                tags: ["speaker", "bluetooth", "portable", "audio"]
            },
            {
                id: 8,
                name: { en: "Wireless Mouse", ar: "فأرة لاسلكية" },
                description: { 
                    en: "Ergonomic wireless mouse with precision tracking, programmable buttons, and long battery life.", 
                    ar: "فأرة لاسلكية مريحة مع تتبع دقيق وأزرار قابلة للبرمجة وبطارية طويلة المدى." 
                },
                price: 49.99,
                originalPrice: 69.99,
                image: "https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=300&fit=crop",
                category: "electronics",
                rating: 4.2,
                reviews: 89,
                inStock: true,
                stockQuantity: 35,
                featured: false,
                tags: ["mouse", "wireless", "computer", "gaming"]
            }
        ];
        
        this.sampleUsers = [
            {
                id: 1,
                firstName: "John",
                lastName: "Doe",
                email: "<EMAIL>",
                password: "password123",
                phone: "******-0123",
                address: "123 Main St",
                city: "New York",
                country: "USA",
                isAdmin: false,
                createdAt: "2024-01-15T10:30:00Z"
            },
            {
                id: 2,
                firstName: "Jane",
                lastName: "Smith",
                email: "<EMAIL>",
                password: "password123",
                phone: "******-0124",
                address: "456 Oak Ave",
                city: "Los Angeles",
                country: "USA",
                isAdmin: false,
                createdAt: "2024-01-20T14:15:00Z"
            },
            {
                id: 3,
                firstName: "Ahmed",
                lastName: "Hassan",
                email: "<EMAIL>",
                password: "password123",
                phone: "+966-50-123-4567",
                address: "789 King Fahd Road",
                city: "Riyadh",
                country: "Saudi Arabia",
                isAdmin: false,
                createdAt: "2024-02-01T09:45:00Z"
            }
        ];
        
        this.sampleOrders = [
            {
                id: 1001,
                userId: 1,
                customerName: "John Doe",
                customerEmail: "<EMAIL>",
                items: [
                    { productId: 1, quantity: 1, price: 1299.99 },
                    { productId: 2, quantity: 1, price: 299.99 }
                ],
                subtotal: 1599.98,
                shipping: 0,
                tax: 127.99,
                total: 1727.97,
                status: "delivered",
                createdAt: "2024-02-15T10:30:00Z",
                shippingAddress: {
                    street: "123 Main St",
                    city: "New York",
                    state: "NY",
                    zipCode: "10001",
                    country: "USA"
                }
            },
            {
                id: 1002,
                userId: 2,
                customerName: "Jane Smith",
                customerEmail: "<EMAIL>",
                items: [
                    { productId: 3, quantity: 1, price: 399.99 },
                    { productId: 6, quantity: 2, price: 79.99 }
                ],
                subtotal: 559.97,
                shipping: 10,
                tax: 44.79,
                total: 614.76,
                status: "shipped",
                createdAt: "2024-02-20T14:15:00Z",
                shippingAddress: {
                    street: "456 Oak Ave",
                    city: "Los Angeles",
                    state: "CA",
                    zipCode: "90210",
                    country: "USA"
                }
            },
            {
                id: 1003,
                userId: 3,
                customerName: "Ahmed Hassan",
                customerEmail: "<EMAIL>",
                items: [
                    { productId: 4, quantity: 1, price: 899.99 }
                ],
                subtotal: 899.99,
                shipping: 0,
                tax: 71.99,
                total: 971.98,
                status: "processing",
                createdAt: "2024-02-25T09:45:00Z",
                shippingAddress: {
                    street: "789 King Fahd Road",
                    city: "Riyadh",
                    state: "Riyadh",
                    zipCode: "12345",
                    country: "Saudi Arabia"
                }
            },
            {
                id: 1004,
                userId: 1,
                customerName: "John Doe",
                customerEmail: "<EMAIL>",
                items: [
                    { productId: 7, quantity: 1, price: 89.99 },
                    { productId: 8, quantity: 1, price: 49.99 }
                ],
                subtotal: 139.98,
                shipping: 10,
                tax: 11.19,
                total: 161.17,
                status: "pending",
                createdAt: "2024-03-01T16:20:00Z",
                shippingAddress: {
                    street: "123 Main St",
                    city: "New York",
                    state: "NY",
                    zipCode: "10001",
                    country: "USA"
                }
            }
        ];
    }
    
    // Generate and save demo data
    generateDemoData() {
        // Save products
        localStorage.setItem('products', JSON.stringify(this.sampleProducts));
        
        // Save users
        localStorage.setItem('users', JSON.stringify(this.sampleUsers));
        
        // Save orders
        localStorage.setItem('orders', JSON.stringify(this.sampleOrders));
        
        // Create admin user
        const adminUser = {
            id: 999,
            firstName: "Admin",
            lastName: "User",
            email: "<EMAIL>",
            password: "admin123",
            phone: "******-ADMIN",
            address: "Admin Office",
            city: "Admin City",
            country: "Admin Country",
            isAdmin: true,
            createdAt: new Date().toISOString()
        };
        
        const users = JSON.parse(localStorage.getItem('users')) || [];
        const existingAdmin = users.find(user => user.email === adminUser.email);
        
        if (!existingAdmin) {
            users.push(adminUser);
            localStorage.setItem('users', JSON.stringify(users));
        }
        
        console.log('Demo data generated successfully!');
        console.log('Products:', this.sampleProducts.length);
        console.log('Users:', this.sampleUsers.length + 1); // +1 for admin
        console.log('Orders:', this.sampleOrders.length);
        
        return {
            products: this.sampleProducts,
            users: this.sampleUsers,
            orders: this.sampleOrders,
            admin: adminUser
        };
    }
    
    // Clear all demo data
    clearDemoData() {
        localStorage.removeItem('products');
        localStorage.removeItem('users');
        localStorage.removeItem('orders');
        localStorage.removeItem('cart');
        localStorage.removeItem('currentUser');
        localStorage.removeItem('adminLoggedIn');
        localStorage.removeItem('adminUser');
        
        console.log('Demo data cleared successfully!');
    }
    
    // Reset to demo data
    resetToDemo() {
        this.clearDemoData();
        return this.generateDemoData();
    }
    
    // Add random orders for testing
    generateRandomOrders(count = 10) {
        const orders = JSON.parse(localStorage.getItem('orders')) || [];
        const products = JSON.parse(localStorage.getItem('products')) || this.sampleProducts;
        const users = JSON.parse(localStorage.getItem('users')) || this.sampleUsers;
        
        const statuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled'];
        
        for (let i = 0; i < count; i++) {
            const randomUser = users[Math.floor(Math.random() * users.length)];
            const randomProducts = this.getRandomProducts(products, Math.floor(Math.random() * 3) + 1);
            
            const items = randomProducts.map(product => ({
                productId: product.id,
                quantity: Math.floor(Math.random() * 3) + 1,
                price: product.price
            }));
            
            const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const shipping = subtotal > 100 ? 0 : 10;
            const tax = subtotal * 0.08;
            const total = subtotal + shipping + tax;
            
            const order = {
                id: Date.now() + i,
                userId: randomUser.id,
                customerName: `${randomUser.firstName} ${randomUser.lastName}`,
                customerEmail: randomUser.email,
                items: items,
                subtotal: subtotal,
                shipping: shipping,
                tax: tax,
                total: total,
                status: statuses[Math.floor(Math.random() * statuses.length)],
                createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
                shippingAddress: {
                    street: randomUser.address,
                    city: randomUser.city,
                    country: randomUser.country
                }
            };
            
            orders.push(order);
        }
        
        localStorage.setItem('orders', JSON.stringify(orders));
        console.log(`Generated ${count} random orders`);
        
        return orders;
    }
    
    // Helper method to get random products
    getRandomProducts(products, count) {
        const shuffled = [...products].sort(() => 0.5 - Math.random());
        return shuffled.slice(0, count);
    }
    
    // Get demo statistics
    getDemoStats() {
        const products = JSON.parse(localStorage.getItem('products')) || [];
        const users = JSON.parse(localStorage.getItem('users')) || [];
        const orders = JSON.parse(localStorage.getItem('orders')) || [];
        
        const totalSales = orders.reduce((sum, order) => sum + order.total, 0);
        const totalOrders = orders.length;
        const totalCustomers = users.filter(user => !user.isAdmin).length;
        const totalProducts = products.length;
        
        return {
            totalSales,
            totalOrders,
            totalCustomers,
            totalProducts,
            averageOrderValue: totalOrders > 0 ? totalSales / totalOrders : 0
        };
    }
}

// Initialize demo data generator
const demoDataGenerator = new DemoDataGenerator();

// Auto-generate demo data if none exists
document.addEventListener('DOMContentLoaded', function() {
    const existingProducts = localStorage.getItem('products');
    
    if (!existingProducts) {
        console.log('No existing data found. Generating demo data...');
        demoDataGenerator.generateDemoData();
    }
});

// Global functions for console access
window.generateDemoData = () => demoDataGenerator.generateDemoData();
window.clearDemoData = () => demoDataGenerator.clearDemoData();
window.resetToDemo = () => demoDataGenerator.resetToDemo();
window.generateRandomOrders = (count) => demoDataGenerator.generateRandomOrders(count);
window.getDemoStats = () => demoDataGenerator.getDemoStats();

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DemoDataGenerator;
}

// Performance Optimization Module

class PerformanceOptimizer {
    constructor() {
        this.imageCache = new Map();
        this.lazyLoadObserver = null;
        this.performanceMetrics = {
            loadTime: 0,
            renderTime: 0,
            interactionTime: 0
        };
        
        this.init();
    }
    
    init() {
        this.measureLoadTime();
        this.setupLazyLoading();
        this.optimizeImages();
        this.setupCaching();
        this.preloadCriticalResources();
        this.optimizeScrolling();
    }
    
    // Measure page load performance
    measureLoadTime() {
        const startTime = performance.now();
        
        window.addEventListener('load', () => {
            const loadTime = performance.now() - startTime;
            this.performanceMetrics.loadTime = loadTime;
            
            // Log performance metrics
            console.log(`Page load time: ${loadTime.toFixed(2)}ms`);
            
            // Send to analytics (if implemented)
            this.sendPerformanceMetrics();
        });
        
        // Measure First Contentful Paint
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.name === 'first-contentful-paint') {
                        console.log(`First Contentful Paint: ${entry.startTime.toFixed(2)}ms`);
                    }
                }
            });
            observer.observe({ entryTypes: ['paint'] });
        }
    }
    
    // Setup lazy loading for images
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            this.lazyLoadObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        this.loadImage(img);
                        this.lazyLoadObserver.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });
            
            // Observe all images with data-src attribute
            this.observeLazyImages();
        } else {
            // Fallback for browsers without IntersectionObserver
            this.loadAllImages();
        }
    }
    
    observeLazyImages() {
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => {
            this.lazyLoadObserver.observe(img);
        });
    }
    
    loadImage(img) {
        const src = img.dataset.src;
        if (src) {
            // Check cache first
            if (this.imageCache.has(src)) {
                img.src = this.imageCache.get(src);
                img.classList.add('loaded');
            } else {
                // Create new image to preload
                const newImg = new Image();
                newImg.onload = () => {
                    this.imageCache.set(src, src);
                    img.src = src;
                    img.classList.add('loaded');
                };
                newImg.onerror = () => {
                    img.src = 'assets/images/placeholder.jpg'; // Fallback image
                    img.classList.add('error');
                };
                newImg.src = src;
            }
        }
    }
    
    loadAllImages() {
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => this.loadImage(img));
    }
    
    // Optimize images
    optimizeImages() {
        // Add loading="lazy" to images that don't have it
        const images = document.querySelectorAll('img:not([loading])');
        images.forEach(img => {
            img.setAttribute('loading', 'lazy');
        });
        
        // Add proper alt attributes for accessibility
        const imagesWithoutAlt = document.querySelectorAll('img:not([alt])');
        imagesWithoutAlt.forEach(img => {
            img.setAttribute('alt', 'Image');
        });
    }
    
    // Setup caching strategies
    setupCaching() {
        // Cache API responses
        this.setupAPICache();
        
        // Cache static resources
        this.cacheStaticResources();
        
        // Setup service worker (if available)
        this.registerServiceWorker();
    }
    
    setupAPICache() {
        // Simple in-memory cache for API responses
        this.apiCache = new Map();
        
        // Override fetch for caching
        const originalFetch = window.fetch;
        window.fetch = async (url, options = {}) => {
            const cacheKey = `${url}_${JSON.stringify(options)}`;
            
            // Check cache for GET requests
            if (!options.method || options.method === 'GET') {
                if (this.apiCache.has(cacheKey)) {
                    const cached = this.apiCache.get(cacheKey);
                    if (Date.now() - cached.timestamp < 300000) { // 5 minutes
                        return Promise.resolve(new Response(JSON.stringify(cached.data)));
                    }
                }
            }
            
            // Make actual request
            const response = await originalFetch(url, options);
            
            // Cache successful GET responses
            if (response.ok && (!options.method || options.method === 'GET')) {
                const data = await response.clone().json();
                this.apiCache.set(cacheKey, {
                    data: data,
                    timestamp: Date.now()
                });
            }
            
            return response;
        };
    }
    
    cacheStaticResources() {
        // Preload critical CSS
        const criticalCSS = [
            'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css',
            'assets/css/style.css'
        ];
        
        criticalCSS.forEach(href => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'style';
            link.href = href;
            document.head.appendChild(link);
        });
    }
    
    registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('Service Worker registered:', registration);
                })
                .catch(error => {
                    console.log('Service Worker registration failed:', error);
                });
        }
    }
    
    // Preload critical resources
    preloadCriticalResources() {
        // Preload critical fonts
        const criticalFonts = [
            'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap'
        ];
        
        criticalFonts.forEach(href => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'style';
            link.href = href;
            document.head.appendChild(link);
        });
        
        // Preload critical JavaScript
        const criticalJS = [
            'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js'
        ];
        
        criticalJS.forEach(src => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'script';
            link.href = src;
            document.head.appendChild(link);
        });
    }
    
    // Optimize scrolling performance
    optimizeScrolling() {
        let ticking = false;
        
        const optimizedScroll = () => {
            // Perform scroll-related operations here
            this.updateScrollProgress();
            ticking = false;
        };
        
        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(optimizedScroll);
                ticking = true;
            }
        }, { passive: true });
    }
    
    updateScrollProgress() {
        const scrollProgress = (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100;
        
        // Update scroll progress indicator if it exists
        const progressBar = document.querySelector('.scroll-progress');
        if (progressBar) {
            progressBar.style.width = `${scrollProgress}%`;
        }
    }
    
    // Debounce function for performance
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Throttle function for performance
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
    
    // Optimize DOM operations
    batchDOMUpdates(updates) {
        requestAnimationFrame(() => {
            updates.forEach(update => update());
        });
    }
    
    // Memory management
    cleanupMemory() {
        // Clear image cache if it gets too large
        if (this.imageCache.size > 100) {
            const entries = Array.from(this.imageCache.entries());
            const toDelete = entries.slice(0, 50);
            toDelete.forEach(([key]) => this.imageCache.delete(key));
        }
        
        // Clear API cache if it gets too large
        if (this.apiCache.size > 50) {
            const entries = Array.from(this.apiCache.entries());
            const toDelete = entries.slice(0, 25);
            toDelete.forEach(([key]) => this.apiCache.delete(key));
        }
    }
    
    // Send performance metrics to analytics
    sendPerformanceMetrics() {
        // This would typically send to your analytics service
        const metrics = {
            loadTime: this.performanceMetrics.loadTime,
            userAgent: navigator.userAgent,
            timestamp: Date.now(),
            url: window.location.href
        };
        
        console.log('Performance metrics:', metrics);
        
        // Example: Send to Google Analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', 'page_load_time', {
                value: Math.round(this.performanceMetrics.loadTime),
                custom_parameter: 'performance'
            });
        }
    }
    
    // Get current performance metrics
    getMetrics() {
        return {
            ...this.performanceMetrics,
            cacheSize: {
                images: this.imageCache.size,
                api: this.apiCache.size
            },
            memoryUsage: performance.memory ? {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            } : null
        };
    }
    
    // Performance monitoring
    startMonitoring() {
        // Monitor memory usage
        setInterval(() => {
            this.cleanupMemory();
        }, 60000); // Every minute
        
        // Monitor performance
        setInterval(() => {
            const metrics = this.getMetrics();
            if (metrics.memoryUsage && metrics.memoryUsage.used > metrics.memoryUsage.limit * 0.8) {
                console.warn('High memory usage detected');
                this.cleanupMemory();
            }
        }, 30000); // Every 30 seconds
    }
}

// Initialize performance optimizer
const performanceOptimizer = new PerformanceOptimizer();

// Start monitoring
performanceOptimizer.startMonitoring();

// Global functions for debugging
window.getPerformanceMetrics = () => performanceOptimizer.getMetrics();
window.cleanupMemory = () => performanceOptimizer.cleanupMemory();

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceOptimizer;
}

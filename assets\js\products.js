// Products Management System

class ProductManager {
    constructor() {
        this.products = JSON.parse(localStorage.getItem('products')) || [];
        this.categories = JSON.parse(localStorage.getItem('categories')) || this.getDefaultCategories();
        this.currentPage = 1;
        this.productsPerPage = 12;
        this.currentFilters = {
            category: '',
            priceMin: 0,
            priceMax: 10000,
            rating: 0,
            search: ''
        };
        this.sortBy = 'name';
        this.sortOrder = 'asc';
        
        this.init();
    }
    
    init() {
        if (this.products.length === 0) {
            this.products = this.getDefaultProducts();
            this.saveProducts();
        }
        this.setupEventListeners();
    }
    
    getDefaultCategories() {
        return [
            {
                id: 1,
                name: { en: "Electronics", ar: "إلكترونيات" },
                slug: "electronics",
                description: { en: "Latest electronic devices", ar: "أحدث الأجهزة الإلكترونية" }
            },
            {
                id: 2,
                name: { en: "Clothing", ar: "ملابس" },
                slug: "clothing",
                description: { en: "Fashion and apparel", ar: "الأزياء والملابس" }
            },
            {
                id: 3,
                name: { en: "Home & Garden", ar: "المنزل والحديقة" },
                slug: "home-garden",
                description: { en: "Home improvement and garden supplies", ar: "تحسين المنزل ومستلزمات الحديقة" }
            },
            {
                id: 4,
                name: { en: "Sports", ar: "رياضة" },
                slug: "sports",
                description: { en: "Sports equipment and accessories", ar: "معدات وإكسسوارات رياضية" }
            }
        ];
    }
    
    getDefaultProducts() {
        return [
            {
                id: 1,
                name: { en: "Premium Laptop", ar: "لابتوب مميز" },
                description: { 
                    en: "High-performance laptop with Intel i7 processor, 16GB RAM, and 512GB SSD. Perfect for professionals and gamers.", 
                    ar: "لابتوب عالي الأداء مع معالج Intel i7 وذاكرة 16 جيجابايت وقرص SSD 512 جيجابايت. مثالي للمحترفين واللاعبين." 
                },
                price: 1299.99,
                originalPrice: 1499.99,
                image: "assets/images/laptop.jpg",
                images: ["assets/images/laptop.jpg", "assets/images/laptop-2.jpg", "assets/images/laptop-3.jpg"],
                category: "electronics",
                rating: 4.8,
                reviews: 124,
                inStock: true,
                stockQuantity: 15,
                featured: true,
                tags: ["laptop", "computer", "gaming", "professional"],
                specifications: {
                    processor: "Intel Core i7-12700H",
                    memory: "16GB DDR4",
                    storage: "512GB NVMe SSD",
                    display: "15.6\" Full HD",
                    graphics: "NVIDIA RTX 3060"
                }
            },
            {
                id: 2,
                name: { en: "Wireless Headphones", ar: "سماعات لاسلكية" },
                description: { 
                    en: "Premium wireless headphones with active noise cancellation, 30-hour battery life, and superior sound quality.", 
                    ar: "سماعات لاسلكية مميزة مع إلغاء الضوضاء النشط وبطارية تدوم 30 ساعة وجودة صوت فائقة." 
                },
                price: 299.99,
                originalPrice: 399.99,
                image: "assets/images/headphones.jpg",
                images: ["assets/images/headphones.jpg", "assets/images/headphones-2.jpg"],
                category: "electronics",
                rating: 4.6,
                reviews: 89,
                inStock: true,
                stockQuantity: 25,
                featured: true,
                tags: ["headphones", "wireless", "audio", "music"],
                specifications: {
                    connectivity: "Bluetooth 5.0",
                    battery: "30 hours",
                    noiseCancellation: "Active",
                    weight: "250g"
                }
            },
            {
                id: 3,
                name: { en: "Smart Watch", ar: "ساعة ذكية" },
                description: { 
                    en: "Advanced smartwatch with health monitoring, GPS tracking, and 7-day battery life. Compatible with iOS and Android.", 
                    ar: "ساعة ذكية متقدمة مع مراقبة الصحة وتتبع GPS وبطارية تدوم 7 أيام. متوافقة مع iOS و Android." 
                },
                price: 399.99,
                originalPrice: 499.99,
                image: "assets/images/smartwatch.jpg",
                images: ["assets/images/smartwatch.jpg", "assets/images/smartwatch-2.jpg"],
                category: "electronics",
                rating: 4.7,
                reviews: 156,
                inStock: true,
                stockQuantity: 30,
                featured: true,
                tags: ["smartwatch", "fitness", "health", "wearable"],
                specifications: {
                    display: "1.4\" AMOLED",
                    battery: "7 days",
                    waterproof: "5ATM",
                    sensors: "Heart rate, GPS, Accelerometer"
                }
            },
            {
                id: 4,
                name: { en: "Professional Camera", ar: "كاميرا احترافية" },
                description: { 
                    en: "High-resolution DSLR camera with 24MP sensor, 4K video recording, and professional lens kit.", 
                    ar: "كاميرا DSLR عالية الدقة مع مستشعر 24 ميجابكسل وتسجيل فيديو 4K ومجموعة عدسات احترافية." 
                },
                price: 899.99,
                originalPrice: 1199.99,
                image: "assets/images/camera.jpg",
                images: ["assets/images/camera.jpg", "assets/images/camera-2.jpg", "assets/images/camera-3.jpg"],
                category: "electronics",
                rating: 4.9,
                reviews: 78,
                inStock: true,
                stockQuantity: 8,
                featured: true,
                tags: ["camera", "photography", "professional", "DSLR"],
                specifications: {
                    sensor: "24MP APS-C",
                    video: "4K @ 30fps",
                    iso: "100-25600",
                    lens: "18-55mm Kit Lens"
                }
            },
            {
                id: 5,
                name: { en: "Gaming Chair", ar: "كرسي ألعاب" },
                description: { 
                    en: "Ergonomic gaming chair with lumbar support, adjustable height, and premium leather upholstery.", 
                    ar: "كرسي ألعاب مريح مع دعم أسفل الظهر وارتفاع قابل للتعديل وتنجيد جلدي مميز." 
                },
                price: 249.99,
                originalPrice: 349.99,
                image: "assets/images/gaming-chair.jpg",
                images: ["assets/images/gaming-chair.jpg", "assets/images/gaming-chair-2.jpg"],
                category: "home-garden",
                rating: 4.5,
                reviews: 92,
                inStock: true,
                stockQuantity: 12,
                featured: false,
                tags: ["chair", "gaming", "ergonomic", "furniture"],
                specifications: {
                    material: "Premium Leather",
                    weight: "Max 150kg",
                    adjustment: "Height, Armrest, Recline",
                    warranty: "2 years"
                }
            },
            {
                id: 6,
                name: { en: "Fitness Tracker", ar: "متتبع اللياقة" },
                description: { 
                    en: "Lightweight fitness tracker with heart rate monitoring, sleep tracking, and 10-day battery life.", 
                    ar: "متتبع لياقة خفيف الوزن مع مراقبة معدل ضربات القلب وتتبع النوم وبطارية تدوم 10 أيام." 
                },
                price: 79.99,
                originalPrice: 99.99,
                image: "assets/images/fitness-tracker.jpg",
                images: ["assets/images/fitness-tracker.jpg"],
                category: "sports",
                rating: 4.3,
                reviews: 203,
                inStock: true,
                stockQuantity: 45,
                featured: false,
                tags: ["fitness", "tracker", "health", "sports"],
                specifications: {
                    display: "0.96\" Color",
                    battery: "10 days",
                    waterproof: "IP68",
                    sensors: "Heart rate, Sleep, Steps"
                }
            }
        ];
    }
    
    // Product CRUD Operations
    getAllProducts() {
        return this.products;
    }
    
    getProduct(id) {
        return this.products.find(product => product.id === parseInt(id));
    }
    
    addProduct(productData) {
        const newProduct = {
            id: Date.now(),
            ...productData,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        this.products.push(newProduct);
        this.saveProducts();
        return newProduct;
    }
    
    updateProduct(id, productData) {
        const index = this.products.findIndex(product => product.id === parseInt(id));
        if (index !== -1) {
            this.products[index] = {
                ...this.products[index],
                ...productData,
                updatedAt: new Date().toISOString()
            };
            this.saveProducts();
            return this.products[index];
        }
        return null;
    }
    
    deleteProduct(id) {
        const index = this.products.findIndex(product => product.id === parseInt(id));
        if (index !== -1) {
            const deletedProduct = this.products.splice(index, 1)[0];
            this.saveProducts();
            return deletedProduct;
        }
        return null;
    }
    
    // Filtering and Searching
    getFilteredProducts() {
        let filtered = [...this.products];
        
        // Category filter
        if (this.currentFilters.category) {
            filtered = filtered.filter(product => product.category === this.currentFilters.category);
        }
        
        // Price filter
        filtered = filtered.filter(product => 
            product.price >= this.currentFilters.priceMin && 
            product.price <= this.currentFilters.priceMax
        );
        
        // Rating filter
        if (this.currentFilters.rating > 0) {
            filtered = filtered.filter(product => product.rating >= this.currentFilters.rating);
        }
        
        // Search filter
        if (this.currentFilters.search) {
            const searchTerm = this.currentFilters.search.toLowerCase();
            filtered = filtered.filter(product => {
                const currentLang = getCurrentLanguage();
                const name = product.name[currentLang] || product.name.en;
                const description = product.description[currentLang] || product.description.en;
                
                return name.toLowerCase().includes(searchTerm) ||
                       description.toLowerCase().includes(searchTerm) ||
                       product.tags.some(tag => tag.toLowerCase().includes(searchTerm));
            });
        }
        
        // Sorting
        filtered.sort((a, b) => {
            let aValue, bValue;
            
            switch (this.sortBy) {
                case 'name':
                    const currentLang = getCurrentLanguage();
                    aValue = a.name[currentLang] || a.name.en;
                    bValue = b.name[currentLang] || b.name.en;
                    break;
                case 'price':
                    aValue = a.price;
                    bValue = b.price;
                    break;
                case 'rating':
                    aValue = a.rating;
                    bValue = b.rating;
                    break;
                case 'date':
                    aValue = new Date(a.createdAt || 0);
                    bValue = new Date(b.createdAt || 0);
                    break;
                default:
                    return 0;
            }
            
            if (this.sortOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });
        
        return filtered;
    }
    
    getPaginatedProducts() {
        const filtered = this.getFilteredProducts();
        const startIndex = (this.currentPage - 1) * this.productsPerPage;
        const endIndex = startIndex + this.productsPerPage;
        
        return {
            products: filtered.slice(startIndex, endIndex),
            totalProducts: filtered.length,
            totalPages: Math.ceil(filtered.length / this.productsPerPage),
            currentPage: this.currentPage
        };
    }
    
    // Filter Management
    setFilter(filterName, value) {
        this.currentFilters[filterName] = value;
        this.currentPage = 1; // Reset to first page when filtering
    }
    
    clearFilters() {
        this.currentFilters = {
            category: '',
            priceMin: 0,
            priceMax: 10000,
            rating: 0,
            search: ''
        };
        this.currentPage = 1;
    }
    
    setSorting(sortBy, sortOrder = 'asc') {
        this.sortBy = sortBy;
        this.sortOrder = sortOrder;
    }
    
    setPage(page) {
        this.currentPage = page;
    }
    
    // Categories
    getCategories() {
        return this.categories;
    }
    
    getCategory(slug) {
        return this.categories.find(category => category.slug === slug);
    }
    
    // Featured Products
    getFeaturedProducts() {
        return this.products.filter(product => product.featured);
    }
    
    // Stock Management
    updateStock(productId, quantity) {
        const product = this.getProduct(productId);
        if (product) {
            product.stockQuantity = quantity;
            product.inStock = quantity > 0;
            this.saveProducts();
            return true;
        }
        return false;
    }
    
    decreaseStock(productId, quantity = 1) {
        const product = this.getProduct(productId);
        if (product && product.stockQuantity >= quantity) {
            product.stockQuantity -= quantity;
            product.inStock = product.stockQuantity > 0;
            this.saveProducts();
            return true;
        }
        return false;
    }
    
    // Storage
    saveProducts() {
        localStorage.setItem('products', JSON.stringify(this.products));
    }
    
    saveCategories() {
        localStorage.setItem('categories', JSON.stringify(this.categories));
    }
    
    // Event Listeners
    setupEventListeners() {
        // Listen for language changes
        document.addEventListener('languageChanged', () => {
            this.refreshProductDisplay();
        });
    }
    
    refreshProductDisplay() {
        // This method can be called to refresh product displays when language changes
        const event = new CustomEvent('productsUpdated');
        document.dispatchEvent(event);
    }
    
    // Product Card Generation
    createProductCard(product, options = {}) {
        const currentLang = getCurrentLanguage();
        const productName = product.name[currentLang] || product.name.en;
        const productDesc = product.description[currentLang] || product.description.en;
        
        const card = document.createElement('div');
        card.className = options.className || 'col-lg-3 col-md-6 mb-4';
        
        const discountPercentage = product.originalPrice ? 
            Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100) : 0;
        
        card.innerHTML = `
            <div class="product-card h-100">
                ${discountPercentage > 0 ? `<div class="badge bg-danger position-absolute" style="top: 10px; right: 10px; z-index: 10;">-${discountPercentage}%</div>` : ''}
                <div class="product-image">
                    <img src="${product.image}" alt="${productName}" class="img-fluid">
                    <div class="product-overlay">
                        <button class="btn btn-light btn-sm me-2" onclick="viewProduct(${product.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="addToCart(${product.id})">
                            <i class="fas fa-shopping-cart"></i>
                        </button>
                    </div>
                </div>
                <div class="product-info">
                    <h5 class="product-title">${productName}</h5>
                    <p class="text-muted small">${productDesc.substring(0, 80)}...</p>
                    <div class="product-rating mb-2">
                        <div class="stars">
                            ${'★'.repeat(Math.floor(product.rating))}${'☆'.repeat(5 - Math.floor(product.rating))}
                        </div>
                        <span class="text-muted small">(${product.reviews})</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="product-price">$${product.price}</div>
                            ${product.originalPrice ? `<small class="text-muted text-decoration-line-through">$${product.originalPrice}</small>` : ''}
                        </div>
                        <div class="text-end">
                            ${product.inStock ? 
                                `<small class="text-success">${getTranslation('inStock')}</small>` : 
                                `<small class="text-danger">${getTranslation('outOfStock')}</small>`
                            }
                        </div>
                    </div>
                    <button class="btn btn-primary w-100 mt-2" onclick="addToCart(${product.id})" ${!product.inStock ? 'disabled' : ''}>
                        <i class="fas fa-cart-plus"></i>
                        ${getTranslation('addToCart')}
                    </button>
                </div>
            </div>
        `;
        
        return card;
    }
}

// Initialize product manager
const productManager = new ProductManager();

// Global product functions
function addToCart(productId) {
    if (typeof app !== 'undefined') {
        app.addToCart(productId);
    }
}

function viewProduct(productId) {
    window.location.href = `product-details.html?id=${productId}`;
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProductManager;
}

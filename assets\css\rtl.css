/* <PERSON><PERSON> (Arabic) Support Styles */

/* RTL Body Styles */
body[dir="rtl"] {
    font-family: '<PERSON><PERSON><PERSON>', 'Inter', sans-serif;
    text-align: right;
}

/* RTL Language Switcher */
body[dir="rtl"] .language-switcher {
    right: auto;
    left: 20px;
}

/* RTL Navigation */
body[dir="rtl"] .navbar-brand {
    margin-right: 0;
    margin-left: auto;
}

body[dir="rtl"] .navbar-nav {
    margin-right: auto;
    margin-left: 0;
}

body[dir="rtl"] .navbar-nav .nav-link {
    text-align: right;
}

body[dir="rtl"] .dropdown-menu {
    right: 0;
    left: auto;
}

/* RTL Hero Section */
body[dir="rtl"] .hero-section .row {
    flex-direction: row-reverse;
}

body[dir="rtl"] .hero-section .col-lg-6:first-child {
    text-align: right;
}

/* RTL Feature Cards */
body[dir="rtl"] .feature-card {
    text-align: right;
}

body[dir="rtl"] .feature-icon {
    margin-left: 0;
    margin-right: 0;
}

/* RTL Product Cards */
body[dir="rtl"] .product-info {
    text-align: right;
}

body[dir="rtl"] .product-rating {
    justify-content: flex-end;
}

body[dir="rtl"] .stars {
    order: 2;
}

body[dir="rtl"] .rating-text {
    order: 1;
    margin-left: 0.5rem;
    margin-right: 0;
}

/* RTL Buttons */
body[dir="rtl"] .btn {
    text-align: center;
}

body[dir="rtl"] .btn i {
    margin-left: 0.5rem;
    margin-right: 0;
}

body[dir="rtl"] .btn i.fa-arrow-right {
    transform: rotate(180deg);
}

/* RTL Forms */
body[dir="rtl"] .form-control {
    text-align: right;
}

body[dir="rtl"] .input-group .form-control {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

body[dir="rtl"] .input-group .btn {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

/* RTL Footer */
body[dir="rtl"] footer {
    text-align: right;
}

body[dir="rtl"] footer .col-md-6:last-child {
    text-align: left;
}

body[dir="rtl"] .social-links {
    text-align: right;
}

body[dir="rtl"] .social-links a {
    margin-left: 0.75rem;
    margin-right: 0;
}

/* RTL Lists */
body[dir="rtl"] ul,
body[dir="rtl"] ol {
    padding-right: 1.5rem;
    padding-left: 0;
}

body[dir="rtl"] .list-unstyled {
    padding-right: 0;
}

/* RTL Breadcrumb */
body[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
    content: "\\";
    transform: scaleX(-1);
}

/* RTL Tables */
body[dir="rtl"] .table {
    text-align: right;
}

body[dir="rtl"] .table th,
body[dir="rtl"] .table td {
    text-align: right;
}

/* RTL Cards */
body[dir="rtl"] .card-body {
    text-align: right;
}

body[dir="rtl"] .card-title {
    text-align: right;
}

/* RTL Modals */
body[dir="rtl"] .modal-header {
    text-align: right;
}

body[dir="rtl"] .modal-body {
    text-align: right;
}

body[dir="rtl"] .modal-footer {
    justify-content: flex-start;
}

body[dir="rtl"] .modal-footer .btn {
    margin-left: 0;
    margin-right: 0.5rem;
}

/* RTL Admin Panel */
body[dir="rtl"] .admin-sidebar {
    right: 0;
    left: auto;
}

body[dir="rtl"] .admin-sidebar .nav-link {
    text-align: right;
}

body[dir="rtl"] .admin-content {
    margin-right: 250px;
    margin-left: 0;
}

/* RTL Cart */
body[dir="rtl"] .cart-item {
    text-align: right;
}

body[dir="rtl"] .cart-item .product-info {
    text-align: right;
}

body[dir="rtl"] .cart-item .quantity-controls {
    justify-content: flex-end;
}

/* RTL Checkout */
body[dir="rtl"] .checkout-form {
    text-align: right;
}

body[dir="rtl"] .order-summary {
    text-align: right;
}

/* RTL Product Details */
body[dir="rtl"] .product-details {
    text-align: right;
}

body[dir="rtl"] .product-gallery {
    order: 2;
}

body[dir="rtl"] .product-info-details {
    order: 1;
}

/* RTL Pagination */
body[dir="rtl"] .pagination {
    justify-content: flex-end;
}

/* RTL Alerts */
body[dir="rtl"] .alert {
    text-align: right;
}

body[dir="rtl"] .alert-dismissible .btn-close {
    left: 0;
    right: auto;
}

/* RTL Tooltips */
body[dir="rtl"] .tooltip {
    text-align: right;
}

/* RTL Dropdowns */
body[dir="rtl"] .dropdown-menu {
    text-align: right;
}

body[dir="rtl"] .dropdown-item {
    text-align: right;
}

/* RTL Progress Bars */
body[dir="rtl"] .progress {
    direction: ltr;
}

/* RTL Badges */
body[dir="rtl"] .badge {
    direction: ltr;
}

/* RTL Responsive Adjustments */
@media (max-width: 768px) {
    body[dir="rtl"] .language-switcher {
        left: 10px;
        right: auto;
    }
    
    body[dir="rtl"] .navbar-collapse {
        text-align: right;
    }
    
    body[dir="rtl"] .hero-section {
        text-align: center;
    }
    
    body[dir="rtl"] .hero-section .col-lg-6:first-child {
        text-align: center;
    }
}

/* RTL Animation Adjustments */
body[dir="rtl"] .slide-in-left {
    animation: slideInRight 0.5s ease-out;
}

body[dir="rtl"] .slide-in-right {
    animation: slideInLeft 0.5s ease-out;
}

/* RTL Icon Adjustments */
body[dir="rtl"] .fa-chevron-right::before {
    content: "\f053";
}

body[dir="rtl"] .fa-chevron-left::before {
    content: "\f054";
}

body[dir="rtl"] .fa-arrow-right::before {
    content: "\f060";
}

body[dir="rtl"] .fa-arrow-left::before {
    content: "\f061";
}

/* RTL Text Alignment Utilities */
body[dir="rtl"] .text-start {
    text-align: right !important;
}

body[dir="rtl"] .text-end {
    text-align: left !important;
}

body[dir="rtl"] .text-md-start {
    text-align: right !important;
}

body[dir="rtl"] .text-md-end {
    text-align: left !important;
}

/* RTL Margin and Padding Adjustments */
body[dir="rtl"] .me-1 { margin-left: 0.25rem !important; margin-right: 0 !important; }
body[dir="rtl"] .me-2 { margin-left: 0.5rem !important; margin-right: 0 !important; }
body[dir="rtl"] .me-3 { margin-left: 1rem !important; margin-right: 0 !important; }
body[dir="rtl"] .me-4 { margin-left: 1.5rem !important; margin-right: 0 !important; }
body[dir="rtl"] .me-5 { margin-left: 3rem !important; margin-right: 0 !important; }

body[dir="rtl"] .ms-1 { margin-right: 0.25rem !important; margin-left: 0 !important; }
body[dir="rtl"] .ms-2 { margin-right: 0.5rem !important; margin-left: 0 !important; }
body[dir="rtl"] .ms-3 { margin-right: 1rem !important; margin-left: 0 !important; }
body[dir="rtl"] .ms-4 { margin-right: 1.5rem !important; margin-left: 0 !important; }
body[dir="rtl"] .ms-5 { margin-right: 3rem !important; margin-left: 0 !important; }

body[dir="rtl"] .pe-1 { padding-left: 0.25rem !important; padding-right: 0 !important; }
body[dir="rtl"] .pe-2 { padding-left: 0.5rem !important; padding-right: 0 !important; }
body[dir="rtl"] .pe-3 { padding-left: 1rem !important; padding-right: 0 !important; }
body[dir="rtl"] .pe-4 { padding-left: 1.5rem !important; padding-right: 0 !important; }
body[dir="rtl"] .pe-5 { padding-left: 3rem !important; padding-right: 0 !important; }

body[dir="rtl"] .ps-1 { padding-right: 0.25rem !important; padding-left: 0 !important; }
body[dir="rtl"] .ps-2 { padding-right: 0.5rem !important; padding-left: 0 !important; }
body[dir="rtl"] .ps-3 { padding-right: 1rem !important; padding-left: 0 !important; }
body[dir="rtl"] .ps-4 { padding-right: 1.5rem !important; padding-left: 0 !important; }
body[dir="rtl"] .ps-5 { padding-right: 3rem !important; padding-left: 0 !important; }

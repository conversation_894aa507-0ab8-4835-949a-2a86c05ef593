<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopping Cart - ModernShop</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/rtl.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body>
    <!-- Language Switcher -->
    <div class="language-switcher">
        <button class="btn btn-sm btn-outline-primary" onclick="toggleLanguage()">
            <i class="fas fa-globe"></i>
            <span id="lang-text">العربية</span>
        </button>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html">
                <i class="fas fa-shopping-bag text-primary me-2"></i>
                <span data-en="ModernShop" data-ar="متجر حديث">ModernShop</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html" data-en="Home" data-ar="الرئيسية">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.html" data-en="Products" data-ar="المنتجات">Products</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="services.html" data-en="Services" data-ar="الخدمات">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html" data-en="About" data-ar="من نحن">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html" data-en="Contact" data-ar="اتصل بنا">Contact</a>
                    </li>
                </ul>
                
                <div class="navbar-nav">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i>
                            <span data-en="Account" data-ar="الحساب">Account</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="login.html" data-en="Login" data-ar="تسجيل الدخول">Login</a></li>
                            <li><a class="dropdown-item" href="register.html" data-en="Register" data-ar="إنشاء حساب">Register</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="profile.html" data-en="My Profile" data-ar="ملفي الشخصي">My Profile</a></li>
                            <li><a class="dropdown-item" href="orders.html" data-en="My Orders" data-ar="طلباتي">My Orders</a></li>
                        </ul>
                    </div>
                    
                    <a class="nav-link position-relative active" href="cart.html">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="cart-count">0</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="bg-light py-3">
        <div class="container">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="index.html" data-en="Home" data-ar="الرئيسية">Home</a></li>
                <li class="breadcrumb-item active" data-en="Shopping Cart" data-ar="سلة التسوق">Shopping Cart</li>
            </ol>
        </div>
    </nav>

    <!-- Cart Section -->
    <section class="cart-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h2 class="mb-4" data-en="Shopping Cart" data-ar="سلة التسوق">Shopping Cart</h2>
                </div>
            </div>
            
            <div class="row">
                <!-- Cart Items -->
                <div class="col-lg-8">
                    <div class="cart-items" id="cart-items">
                        <!-- Cart items will be loaded here -->
                    </div>
                    
                    <!-- Empty Cart Message -->
                    <div class="empty-cart text-center py-5" id="empty-cart" style="display: none;">
                        <i class="fas fa-shopping-cart fa-4x text-muted mb-4"></i>
                        <h4 class="text-muted mb-3" data-en="Your cart is empty" data-ar="سلتك فارغة">Your cart is empty</h4>
                        <p class="text-muted mb-4" data-en="Add some products to get started" data-ar="أضف بعض المنتجات للبدء">Add some products to get started</p>
                        <a href="products.html" class="btn btn-primary" data-en="Continue Shopping" data-ar="متابعة التسوق">Continue Shopping</a>
                    </div>
                </div>
                
                <!-- Cart Summary -->
                <div class="col-lg-4">
                    <div class="cart-summary" id="cart-summary">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0" data-en="Order Summary" data-ar="ملخص الطلب">Order Summary</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-3">
                                    <span data-en="Subtotal" data-ar="المجموع الفرعي">Subtotal</span>
                                    <span id="subtotal">$0.00</span>
                                </div>
                                <div class="d-flex justify-content-between mb-3">
                                    <span data-en="Shipping" data-ar="الشحن">Shipping</span>
                                    <span id="shipping">$0.00</span>
                                </div>
                                <div class="d-flex justify-content-between mb-3">
                                    <span data-en="Tax" data-ar="الضريبة">Tax</span>
                                    <span id="tax">$0.00</span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between mb-4">
                                    <strong data-en="Total" data-ar="المجموع">Total</strong>
                                    <strong id="total">$0.00</strong>
                                </div>
                                
                                <!-- Promo Code -->
                                <div class="mb-4">
                                    <label class="form-label" data-en="Promo Code" data-ar="كود الخصم">Promo Code</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="promo-code" data-en-placeholder="Enter code" data-ar-placeholder="أدخل الكود">
                                        <button class="btn btn-outline-secondary" type="button" onclick="applyPromoCode()" data-en="Apply" data-ar="تطبيق">Apply</button>
                                    </div>
                                </div>
                                
                                <button class="btn btn-primary w-100 mb-3" onclick="proceedToCheckout()" data-en="Proceed to Checkout" data-ar="المتابعة للدفع">
                                    Proceed to Checkout
                                </button>
                                <a href="products.html" class="btn btn-outline-secondary w-100" data-en="Continue Shopping" data-ar="متابعة التسوق">
                                    Continue Shopping
                                </a>
                            </div>
                        </div>
                        
                        <!-- Security Badge -->
                        <div class="security-badge text-center mt-4">
                            <div class="d-flex align-items-center justify-content-center text-muted">
                                <i class="fas fa-shield-alt me-2"></i>
                                <small data-en="Secure checkout guaranteed" data-ar="دفع آمن مضمون">Secure checkout guaranteed</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Recommended Products -->
    <section class="recommended-products py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h3 class="mb-4" data-en="You might also like" data-ar="قد يعجبك أيضاً">You might also like</h3>
                </div>
            </div>
            <div class="row" id="recommended-products">
                <!-- Recommended products will be loaded here -->
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4">
                    <h5 class="fw-bold mb-3" data-en="ModernShop" data-ar="متجر حديث">ModernShop</h5>
                    <p data-en="Your trusted partner for premium products and exceptional service." data-ar="شريكك الموثوق للمنتجات المميزة والخدمة الاستثنائية.">
                        Your trusted partner for premium products and exceptional service.
                    </p>
                    <div class="social-links">
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                
                <div class="col-lg-2">
                    <h6 class="fw-bold mb-3" data-en="Quick Links" data-ar="روابط سريعة">Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="about.html" class="text-white-50" data-en="About Us" data-ar="من نحن">About Us</a></li>
                        <li><a href="contact.html" class="text-white-50" data-en="Contact" data-ar="اتصل بنا">Contact</a></li>
                        <li><a href="faq.html" class="text-white-50" data-en="FAQ" data-ar="الأسئلة الشائعة">FAQ</a></li>
                        <li><a href="privacy.html" class="text-white-50" data-en="Privacy Policy" data-ar="سياسة الخصوصية">Privacy Policy</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-3">
                    <h6 class="fw-bold mb-3" data-en="Contact Info" data-ar="معلومات الاتصال">Contact Info</h6>
                    <p class="text-white-50">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        <span data-en="123 Business Street, City" data-ar="123 شارع الأعمال، المدينة">123 Business Street, City</span>
                    </p>
                    <p class="text-white-50">
                        <i class="fas fa-phone me-2"></i>
                        +****************
                    </p>
                    <p class="text-white-50">
                        <i class="fas fa-envelope me-2"></i>
                        <EMAIL>
                    </p>
                </div>
                
                <div class="col-lg-3">
                    <h6 class="fw-bold mb-3" data-en="Newsletter" data-ar="النشرة الإخبارية">Newsletter</h6>
                    <p class="text-white-50" data-en="Subscribe to get updates on new products and offers." data-ar="اشترك للحصول على تحديثات حول المنتجات والعروض الجديدة.">
                        Subscribe to get updates on new products and offers.
                    </p>
                    <div class="input-group">
                        <input type="email" class="form-control" placeholder="Enter email" data-en-placeholder="Enter email" data-ar-placeholder="أدخل البريد الإلكتروني">
                        <button class="btn btn-primary" type="button" data-en="Subscribe" data-ar="اشترك">Subscribe</button>
                    </div>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-white-50" data-en="© 2024 ModernShop. All rights reserved." data-ar="© 2024 متجر حديث. جميع الحقوق محفوظة.">
                        © 2024 ModernShop. All rights reserved.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="admin/login.html" class="text-white-50 text-decoration-none" data-en="Admin Panel" data-ar="لوحة الإدارة">
                        Admin Panel
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="assets/js/demo-data.js"></script>
    <script src="assets/js/app.js"></script>
    <script src="assets/js/language.js"></script>
    <script src="assets/js/products.js"></script>
    
    <script>
        // Cart page specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            loadCartItems();
            loadRecommendedProducts();
        });
        
        function loadCartItems() {
            const cartItems = app.getCartItems();
            const cartItemsContainer = document.getElementById('cart-items');
            const emptyCart = document.getElementById('empty-cart');
            const cartSummary = document.getElementById('cart-summary');
            
            if (cartItems.length === 0) {
                cartItemsContainer.style.display = 'none';
                cartSummary.style.display = 'none';
                emptyCart.style.display = 'block';
                return;
            }
            
            cartItemsContainer.style.display = 'block';
            cartSummary.style.display = 'block';
            emptyCart.style.display = 'none';
            
            cartItemsContainer.innerHTML = '';
            
            cartItems.forEach(item => {
                const cartItem = createCartItemElement(item);
                cartItemsContainer.appendChild(cartItem);
            });
            
            updateCartSummary();
        }
        
        function createCartItemElement(item) {
            const currentLang = getCurrentLanguage();
            const productName = item.product.name[currentLang] || item.product.name.en;
            const productDesc = item.product.description[currentLang] || item.product.description.en;
            
            const cartItem = document.createElement('div');
            cartItem.className = 'cart-item card mb-3';
            cartItem.innerHTML = `
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            <img src="${item.product.image}" alt="${productName}" class="img-fluid rounded">
                        </div>
                        <div class="col-md-4">
                            <h6 class="mb-1">${productName}</h6>
                            <p class="text-muted small mb-0">${productDesc.substring(0, 100)}...</p>
                            <div class="rating mt-1">
                                ${'★'.repeat(Math.floor(item.product.rating))}${'☆'.repeat(5 - Math.floor(item.product.rating))}
                                <span class="text-muted small">(${item.product.reviews})</span>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="quantity-controls">
                                <div class="input-group input-group-sm">
                                    <button class="btn btn-outline-secondary" type="button" onclick="updateQuantity(${item.productId}, ${item.quantity - 1})">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <input type="number" class="form-control text-center" value="${item.quantity}" min="1" onchange="updateQuantity(${item.productId}, this.value)">
                                    <button class="btn btn-outline-secondary" type="button" onclick="updateQuantity(${item.productId}, ${item.quantity + 1})">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 text-center">
                            <div class="price">
                                <strong>$${(item.product.price * item.quantity).toFixed(2)}</strong>
                            </div>
                            <div class="unit-price text-muted small">
                                $${item.product.price} each
                            </div>
                        </div>
                        <div class="col-md-2 text-center">
                            <button class="btn btn-outline-danger btn-sm" onclick="removeFromCart(${item.productId})" data-en="Remove" data-ar="إزالة">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            return cartItem;
        }
        
        function updateQuantity(productId, quantity) {
            quantity = parseInt(quantity);
            if (quantity <= 0) {
                removeFromCart(productId);
            } else {
                app.updateCartQuantity(productId, quantity);
                loadCartItems();
            }
        }
        
        function removeFromCart(productId) {
            app.removeFromCart(productId);
            loadCartItems();
        }
        
        function updateCartSummary() {
            const cartItems = app.getCartItems();
            const subtotal = app.getCartTotal();
            const shipping = subtotal > 100 ? 0 : 10; // Free shipping over $100
            const tax = subtotal * 0.08; // 8% tax
            const total = subtotal + shipping + tax;
            
            document.getElementById('subtotal').textContent = `$${subtotal.toFixed(2)}`;
            document.getElementById('shipping').textContent = shipping === 0 ? 'Free' : `$${shipping.toFixed(2)}`;
            document.getElementById('tax').textContent = `$${tax.toFixed(2)}`;
            document.getElementById('total').textContent = `$${total.toFixed(2)}`;
        }
        
        function applyPromoCode() {
            const promoCode = document.getElementById('promo-code').value.trim();
            
            if (!promoCode) {
                app.showNotification(
                    getCurrentLanguage() === 'ar' ? 'يرجى إدخال كود الخصم' : 'Please enter a promo code',
                    'warning'
                );
                return;
            }
            
            // Simple promo code validation (in real app, this would be server-side)
            const validCodes = {
                'SAVE10': 0.10,
                'WELCOME20': 0.20,
                'FIRST15': 0.15
            };
            
            if (validCodes[promoCode.toUpperCase()]) {
                const discount = validCodes[promoCode.toUpperCase()];
                app.showNotification(
                    getCurrentLanguage() === 'ar' ? 
                        `تم تطبيق خصم ${Math.round(discount * 100)}%` : 
                        `${Math.round(discount * 100)}% discount applied`,
                    'success'
                );
                // Apply discount logic here
                updateCartSummary();
            } else {
                app.showNotification(
                    getCurrentLanguage() === 'ar' ? 'كود خصم غير صحيح' : 'Invalid promo code',
                    'danger'
                );
            }
        }
        
        function proceedToCheckout() {
            const cartItems = app.getCartItems();
            
            if (cartItems.length === 0) {
                app.showNotification(
                    getCurrentLanguage() === 'ar' ? 'سلتك فارغة' : 'Your cart is empty',
                    'warning'
                );
                return;
            }
            
            // Redirect to checkout page
            window.location.href = 'checkout.html';
        }
        
        function loadRecommendedProducts() {
            const featuredProducts = productManager.getFeaturedProducts().slice(0, 4);
            const container = document.getElementById('recommended-products');
            container.innerHTML = '';
            
            featuredProducts.forEach(product => {
                const productCard = productManager.createProductCard(product, {
                    className: 'col-lg-3 col-md-6 mb-4'
                });
                container.appendChild(productCard);
            });
        }
    </script>
</body>
</html>

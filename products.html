<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - ModernShop</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/rtl.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body>
    <!-- Language Switcher -->
    <div class="language-switcher">
        <button class="btn btn-sm btn-outline-primary" onclick="toggleLanguage()">
            <i class="fas fa-globe"></i>
            <span id="lang-text">العربية</span>
        </button>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html">
                <i class="fas fa-shopping-bag text-primary me-2"></i>
                <span data-en="ModernShop" data-ar="متجر حديث">ModernShop</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html" data-en="Home" data-ar="الرئيسية">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="products.html" data-en="Products" data-ar="المنتجات">Products</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="services.html" data-en="Services" data-ar="الخدمات">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html" data-en="من نحن" data-ar="About">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html" data-en="Contact" data-ar="اتصل بنا">Contact</a>
                    </li>
                </ul>
                
                <div class="navbar-nav">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i>
                            <span data-en="Account" data-ar="الحساب">Account</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="login.html" data-en="Login" data-ar="تسجيل الدخول">Login</a></li>
                            <li><a class="dropdown-item" href="register.html" data-en="Register" data-ar="إنشاء حساب">Register</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="profile.html" data-en="My Profile" data-ar="ملفي الشخصي">My Profile</a></li>
                            <li><a class="dropdown-item" href="orders.html" data-en="My Orders" data-ar="طلباتي">My Orders</a></li>
                        </ul>
                    </div>
                    
                    <a class="nav-link position-relative" href="cart.html">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="cart-count">0</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="bg-light py-3">
        <div class="container">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="index.html" data-en="Home" data-ar="الرئيسية">Home</a></li>
                <li class="breadcrumb-item active" data-en="Products" data-ar="المنتجات">Products</li>
            </ol>
        </div>
    </nav>

    <!-- Products Section -->
    <section class="products-section py-5">
        <div class="container">
            <div class="row">
                <!-- Filters Sidebar -->
                <div class="col-lg-3 mb-4">
                    <div class="filters-sidebar">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0" data-en="Filters" data-ar="المرشحات">Filters</h5>
                            </div>
                            <div class="card-body">
                                <!-- Search -->
                                <div class="mb-4">
                                    <label class="form-label" data-en="Search Products" data-ar="البحث في المنتجات">Search Products</label>
                                    <input type="text" class="form-control" id="search-input" data-en-placeholder="Search..." data-ar-placeholder="البحث...">
                                </div>
                                
                                <!-- Categories -->
                                <div class="mb-4">
                                    <label class="form-label" data-en="Category" data-ar="الفئة">Category</label>
                                    <select class="form-select" id="category-filter">
                                        <option value="" data-en="All Categories" data-ar="جميع الفئات">All Categories</option>
                                        <option value="electronics" data-en="Electronics" data-ar="إلكترونيات">Electronics</option>
                                        <option value="clothing" data-en="Clothing" data-ar="ملابس">Clothing</option>
                                        <option value="home-garden" data-en="Home & Garden" data-ar="المنزل والحديقة">Home & Garden</option>
                                        <option value="sports" data-en="Sports" data-ar="رياضة">Sports</option>
                                    </select>
                                </div>
                                
                                <!-- Price Range -->
                                <div class="mb-4">
                                    <label class="form-label" data-en="Price Range" data-ar="نطاق السعر">Price Range</label>
                                    <div class="row g-2">
                                        <div class="col-6">
                                            <input type="number" class="form-control" id="price-min" placeholder="Min" min="0" value="0">
                                        </div>
                                        <div class="col-6">
                                            <input type="number" class="form-control" id="price-max" placeholder="Max" min="0" value="10000">
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Rating -->
                                <div class="mb-4">
                                    <label class="form-label" data-en="Minimum Rating" data-ar="أقل تقييم">Minimum Rating</label>
                                    <select class="form-select" id="rating-filter">
                                        <option value="0" data-en="All Ratings" data-ar="جميع التقييمات">All Ratings</option>
                                        <option value="4">4+ ★</option>
                                        <option value="3">3+ ★</option>
                                        <option value="2">2+ ★</option>
                                        <option value="1">1+ ★</option>
                                    </select>
                                </div>
                                
                                <!-- Clear Filters -->
                                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()" data-en="Clear Filters" data-ar="مسح المرشحات">
                                    Clear Filters
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Products Grid -->
                <div class="col-lg-9">
                    <!-- Toolbar -->
                    <div class="products-toolbar d-flex justify-content-between align-items-center mb-4">
                        <div class="results-info">
                            <span id="results-count" data-en="Showing all products" data-ar="عرض جميع المنتجات">Showing all products</span>
                        </div>
                        
                        <div class="d-flex gap-3 align-items-center">
                            <!-- View Toggle -->
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-secondary active" id="grid-view" onclick="setView('grid')">
                                    <i class="fas fa-th"></i>
                                </button>
                                <button type="button" class="btn btn-outline-secondary" id="list-view" onclick="setView('list')">
                                    <i class="fas fa-list"></i>
                                </button>
                            </div>
                            
                            <!-- Sort -->
                            <select class="form-select" id="sort-select" style="width: auto;">
                                <option value="name-asc" data-en="Name A-Z" data-ar="الاسم أ-ي">Name A-Z</option>
                                <option value="name-desc" data-en="Name Z-A" data-ar="الاسم ي-أ">Name Z-A</option>
                                <option value="price-asc" data-en="Price Low-High" data-ar="السعر منخفض-عالي">Price Low-High</option>
                                <option value="price-desc" data-en="Price High-Low" data-ar="السعر عالي-منخفض">Price High-Low</option>
                                <option value="rating-desc" data-en="Highest Rated" data-ar="الأعلى تقييماً">Highest Rated</option>
                                <option value="date-desc" data-en="Newest First" data-ar="الأحدث أولاً">Newest First</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Products Grid -->
                    <div class="row" id="products-grid">
                        <!-- Products will be loaded here -->
                    </div>
                    
                    <!-- Pagination -->
                    <nav aria-label="Products pagination" class="mt-5">
                        <ul class="pagination justify-content-center" id="pagination">
                            <!-- Pagination will be generated here -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4">
                    <h5 class="fw-bold mb-3" data-en="ModernShop" data-ar="متجر حديث">ModernShop</h5>
                    <p data-en="Your trusted partner for premium products and exceptional service." data-ar="شريكك الموثوق للمنتجات المميزة والخدمة الاستثنائية.">
                        Your trusted partner for premium products and exceptional service.
                    </p>
                    <div class="social-links">
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                
                <div class="col-lg-2">
                    <h6 class="fw-bold mb-3" data-en="Quick Links" data-ar="روابط سريعة">Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="about.html" class="text-white-50" data-en="About Us" data-ar="من نحن">About Us</a></li>
                        <li><a href="contact.html" class="text-white-50" data-en="Contact" data-ar="اتصل بنا">Contact</a></li>
                        <li><a href="faq.html" class="text-white-50" data-en="FAQ" data-ar="الأسئلة الشائعة">FAQ</a></li>
                        <li><a href="privacy.html" class="text-white-50" data-en="Privacy Policy" data-ar="سياسة الخصوصية">Privacy Policy</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-3">
                    <h6 class="fw-bold mb-3" data-en="Contact Info" data-ar="معلومات الاتصال">Contact Info</h6>
                    <p class="text-white-50">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        <span data-en="123 Business Street, City" data-ar="123 شارع الأعمال، المدينة">123 Business Street, City</span>
                    </p>
                    <p class="text-white-50">
                        <i class="fas fa-phone me-2"></i>
                        +****************
                    </p>
                    <p class="text-white-50">
                        <i class="fas fa-envelope me-2"></i>
                        <EMAIL>
                    </p>
                </div>
                
                <div class="col-lg-3">
                    <h6 class="fw-bold mb-3" data-en="Newsletter" data-ar="النشرة الإخبارية">Newsletter</h6>
                    <p class="text-white-50" data-en="Subscribe to get updates on new products and offers." data-ar="اشترك للحصول على تحديثات حول المنتجات والعروض الجديدة.">
                        Subscribe to get updates on new products and offers.
                    </p>
                    <div class="input-group">
                        <input type="email" class="form-control" placeholder="Enter email" data-en-placeholder="Enter email" data-ar-placeholder="أدخل البريد الإلكتروني">
                        <button class="btn btn-primary" type="button" data-en="Subscribe" data-ar="اشترك">Subscribe</button>
                    </div>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-white-50" data-en="© 2024 ModernShop. All rights reserved." data-ar="© 2024 متجر حديث. جميع الحقوق محفوظة.">
                        © 2024 ModernShop. All rights reserved.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="admin/login.html" class="text-white-50 text-decoration-none" data-en="Admin Panel" data-ar="لوحة الإدارة">
                        Admin Panel
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="assets/js/demo-data.js"></script>
    <script src="assets/js/app.js"></script>
    <script src="assets/js/language.js"></script>
    <script src="assets/js/products.js"></script>
    
    <script>
        // Products page specific JavaScript
        let currentView = 'grid';
        
        document.addEventListener('DOMContentLoaded', function() {
            loadProducts();
            setupFilters();
        });
        
        function loadProducts() {
            const paginatedData = productManager.getPaginatedProducts();
            displayProducts(paginatedData.products);
            updateResultsCount(paginatedData.totalProducts);
            generatePagination(paginatedData.totalPages, paginatedData.currentPage);
        }
        
        function displayProducts(products) {
            const grid = document.getElementById('products-grid');
            grid.innerHTML = '';
            
            if (products.length === 0) {
                grid.innerHTML = `
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted" data-en="No products found" data-ar="لم يتم العثور على منتجات">No products found</h4>
                        <p class="text-muted" data-en="Try adjusting your filters" data-ar="جرب تعديل المرشحات">Try adjusting your filters</p>
                    </div>
                `;
                return;
            }
            
            products.forEach(product => {
                const productCard = productManager.createProductCard(product, {
                    className: currentView === 'grid' ? 'col-lg-4 col-md-6 mb-4' : 'col-12 mb-3'
                });
                grid.appendChild(productCard);
            });
        }
        
        function setupFilters() {
            // Search filter
            document.getElementById('search-input').addEventListener('input', function() {
                productManager.setFilter('search', this.value);
                loadProducts();
            });
            
            // Category filter
            document.getElementById('category-filter').addEventListener('change', function() {
                productManager.setFilter('category', this.value);
                loadProducts();
            });
            
            // Price filters
            document.getElementById('price-min').addEventListener('change', function() {
                productManager.setFilter('priceMin', parseFloat(this.value) || 0);
                loadProducts();
            });
            
            document.getElementById('price-max').addEventListener('change', function() {
                productManager.setFilter('priceMax', parseFloat(this.value) || 10000);
                loadProducts();
            });
            
            // Rating filter
            document.getElementById('rating-filter').addEventListener('change', function() {
                productManager.setFilter('rating', parseFloat(this.value) || 0);
                loadProducts();
            });
            
            // Sort
            document.getElementById('sort-select').addEventListener('change', function() {
                const [sortBy, sortOrder] = this.value.split('-');
                productManager.setSorting(sortBy, sortOrder);
                loadProducts();
            });
        }
        
        function clearFilters() {
            productManager.clearFilters();
            document.getElementById('search-input').value = '';
            document.getElementById('category-filter').value = '';
            document.getElementById('price-min').value = '0';
            document.getElementById('price-max').value = '10000';
            document.getElementById('rating-filter').value = '0';
            loadProducts();
        }
        
        function setView(view) {
            currentView = view;
            document.getElementById('grid-view').classList.toggle('active', view === 'grid');
            document.getElementById('list-view').classList.toggle('active', view === 'list');
            loadProducts();
        }
        
        function updateResultsCount(count) {
            const resultsElement = document.getElementById('results-count');
            const currentLang = getCurrentLanguage();
            const text = currentLang === 'ar' ? 
                `عرض ${count} منتج` : 
                `Showing ${count} products`;
            resultsElement.textContent = text;
        }
        
        function generatePagination(totalPages, currentPage) {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';
            
            if (totalPages <= 1) return;
            
            // Previous button
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage - 1})">Previous</a>`;
            pagination.appendChild(prevLi);
            
            // Page numbers
            for (let i = 1; i <= totalPages; i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
                pagination.appendChild(li);
            }
            
            // Next button
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage + 1})">Next</a>`;
            pagination.appendChild(nextLi);
        }
        
        function changePage(page) {
            productManager.setPage(page);
            loadProducts();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - ModernShop</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/rtl.css">
    
    <style>
        .admin-login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        
        .login-header {
            background: var(--primary-color);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-body {
            padding: 2rem;
        }
        
        .form-floating .form-control {
            border-radius: var(--border-radius);
        }
        
        .btn-login {
            background: var(--primary-color);
            border: none;
            border-radius: var(--border-radius);
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: var(--transition);
        }
        
        .btn-login:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        
        .back-to-site {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
        }
        
        .back-to-site:hover {
            color: rgba(255, 255, 255, 0.8);
        }
        
        .language-switcher {
            position: absolute;
            top: 20px;
            right: 20px;
        }
        
        .language-switcher .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
        }
        
        .language-switcher .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Language Switcher -->
    <div class="language-switcher">
        <button class="btn btn-sm" onclick="toggleLanguage()">
            <i class="fas fa-globe"></i>
            <span id="lang-text">العربية</span>
        </button>
    </div>
    
    <!-- Back to Site -->
    <a href="../index.html" class="back-to-site">
        <i class="fas fa-arrow-left me-2"></i>
        <span data-en="Back to Site" data-ar="العودة للموقع">Back to Site</span>
    </a>

    <div class="admin-login-container">
        <div class="login-card">
            <div class="login-header">
                <i class="fas fa-shield-alt fa-3x mb-3"></i>
                <h3 class="mb-0" data-en="Admin Panel" data-ar="لوحة الإدارة">Admin Panel</h3>
                <p class="mb-0 opacity-75" data-en="Secure Login" data-ar="تسجيل دخول آمن">Secure Login</p>
            </div>
            
            <div class="login-body">
                <form id="admin-login-form">
                    <div class="form-floating mb-3">
                        <input type="email" class="form-control" id="admin-email" placeholder="Email" required>
                        <label for="admin-email" data-en="Email Address" data-ar="البريد الإلكتروني">Email Address</label>
                    </div>
                    
                    <div class="form-floating mb-4">
                        <input type="password" class="form-control" id="admin-password" placeholder="Password" required>
                        <label for="admin-password" data-en="Password" data-ar="كلمة المرور">Password</label>
                    </div>
                    
                    <div class="form-check mb-4">
                        <input class="form-check-input" type="checkbox" id="remember-me">
                        <label class="form-check-label" for="remember-me" data-en="Remember me" data-ar="تذكرني">
                            Remember me
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-login w-100 mb-3">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        <span data-en="Login to Dashboard" data-ar="دخول لوحة التحكم">Login to Dashboard</span>
                    </button>
                    
                    <div class="text-center">
                        <a href="#" class="text-muted text-decoration-none" data-en="Forgot Password?" data-ar="نسيت كلمة المرور؟">
                            Forgot Password?
                        </a>
                    </div>
                </form>
                
                <!-- Demo Credentials -->
                <div class="alert alert-info mt-4">
                    <h6 class="alert-heading" data-en="Demo Credentials" data-ar="بيانات تجريبية">Demo Credentials</h6>
                    <p class="mb-1"><strong data-en="Email:" data-ar="البريد:">Email:</strong> <EMAIL></p>
                    <p class="mb-0"><strong data-en="Password:" data-ar="كلمة المرور:">Password:</strong> admin123</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="../assets/js/language.js"></script>
    
    <script>
        // Admin login functionality
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('admin-login-form');
            
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const email = document.getElementById('admin-email').value;
                const password = document.getElementById('admin-password').value;
                
                // Simple demo authentication
                if (email === '<EMAIL>' && password === 'admin123') {
                    // Store admin session
                    localStorage.setItem('adminLoggedIn', 'true');
                    localStorage.setItem('adminUser', JSON.stringify({
                        email: email,
                        name: 'Admin User',
                        role: 'administrator',
                        loginTime: new Date().toISOString()
                    }));
                    
                    // Show success message
                    showNotification(
                        getCurrentLanguage() === 'ar' ? 'تم تسجيل الدخول بنجاح' : 'Login successful',
                        'success'
                    );
                    
                    // Redirect to dashboard
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 1000);
                } else {
                    showNotification(
                        getCurrentLanguage() === 'ar' ? 'بيانات دخول غير صحيحة' : 'Invalid credentials',
                        'danger'
                    );
                }
            });
        });
        
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 1060; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(notification);
            
            // Auto remove after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }
        
        // Auto-fill demo credentials
        function fillDemoCredentials() {
            document.getElementById('admin-email').value = '<EMAIL>';
            document.getElementById('admin-password').value = 'admin123';
        }
        
        // Add click handler to demo credentials
        document.addEventListener('DOMContentLoaded', function() {
            const demoAlert = document.querySelector('.alert-info');
            if (demoAlert) {
                demoAlert.style.cursor = 'pointer';
                demoAlert.addEventListener('click', fillDemoCredentials);
                demoAlert.title = getCurrentLanguage() === 'ar' ? 
                    'انقر لملء البيانات التجريبية' : 
                    'Click to fill demo credentials';
            }
        });
    </script>
</body>
</html>

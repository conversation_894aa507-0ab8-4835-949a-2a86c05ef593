<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - ModernShop</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/rtl.css">
    
    <style>
        .admin-wrapper {
            display: flex;
            min-height: 100vh;
        }
        
        .admin-sidebar {
            width: 250px;
            background: #1f2937;
            color: white;
            flex-shrink: 0;
        }
        
        .admin-content {
            flex: 1;
            background: #f8fafc;
            overflow-x: auto;
        }
        
        .admin-header {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: between;
            align-items: center;
        }
        
        .sidebar-brand {
            padding: 1.5rem;
            border-bottom: 1px solid #374151;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .sidebar-nav .nav-link {
            color: #d1d5db;
            padding: 0.75rem 1.5rem;
            display: flex;
            align-items: center;
            text-decoration: none;
            transition: var(--transition);
        }
        
        .sidebar-nav .nav-link:hover,
        .sidebar-nav .nav-link.active {
            background: var(--primary-color);
            color: white;
        }
        
        .sidebar-nav .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
        }
        
        .stat-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid #e5e7eb;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }
        
        .stat-change {
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .stat-change.positive {
            color: #10b981;
        }
        
        .stat-change.negative {
            color: #ef4444;
        }
        
        .chart-container {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid #e5e7eb;
        }
        
        .recent-orders {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid #e5e7eb;
        }
        
        .language-switcher {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
        }
        
        @media (max-width: 768px) {
            .admin-sidebar {
                width: 100%;
                position: fixed;
                top: 0;
                left: -100%;
                z-index: 1040;
                transition: left 0.3s ease;
            }
            
            .admin-sidebar.show {
                left: 0;
            }
            
            .admin-content {
                margin-left: 0;
            }
            
            .mobile-menu-btn {
                display: block !important;
            }
        }
        
        .mobile-menu-btn {
            display: none;
        }
    </style>
</head>
<body>
    <!-- Language Switcher -->
    <div class="language-switcher">
        <button class="btn btn-sm btn-outline-primary" onclick="toggleLanguage()">
            <i class="fas fa-globe"></i>
            <span id="lang-text">العربية</span>
        </button>
    </div>

    <div class="admin-wrapper">
        <!-- Sidebar -->
        <nav class="admin-sidebar" id="admin-sidebar">
            <div class="sidebar-brand">
                <h4 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    <span data-en="Admin Panel" data-ar="لوحة الإدارة">Admin Panel</span>
                </h4>
            </div>
            
            <div class="sidebar-nav">
                <a href="dashboard.html" class="nav-link active">
                    <i class="fas fa-tachometer-alt"></i>
                    <span data-en="Dashboard" data-ar="لوحة التحكم">Dashboard</span>
                </a>
                <a href="products.html" class="nav-link">
                    <i class="fas fa-box"></i>
                    <span data-en="Products" data-ar="المنتجات">Products</span>
                </a>
                <a href="orders.html" class="nav-link">
                    <i class="fas fa-shopping-cart"></i>
                    <span data-en="Orders" data-ar="الطلبات">Orders</span>
                </a>
                <a href="customers.html" class="nav-link">
                    <i class="fas fa-users"></i>
                    <span data-en="Customers" data-ar="العملاء">Customers</span>
                </a>
                <a href="analytics.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    <span data-en="Analytics" data-ar="التحليلات">Analytics</span>
                </a>
                <a href="settings.html" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span data-en="Settings" data-ar="الإعدادات">Settings</span>
                </a>
                
                <hr class="my-3" style="border-color: #374151;">
                
                <a href="../index.html" class="nav-link">
                    <i class="fas fa-external-link-alt"></i>
                    <span data-en="View Site" data-ar="عرض الموقع">View Site</span>
                </a>
                <a href="#" class="nav-link" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    <span data-en="Logout" data-ar="تسجيل الخروج">Logout</span>
                </a>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <div class="d-flex align-items-center">
                    <button class="btn btn-outline-secondary mobile-menu-btn me-3" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h2 class="mb-0" data-en="Dashboard Overview" data-ar="نظرة عامة على لوحة التحكم">Dashboard Overview</h2>
                </div>
                
                <div class="d-flex align-items-center">
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-2"></i>
                            <span data-en="Admin User" data-ar="مستخدم الإدارة">Admin User</span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.html" data-en="Profile" data-ar="الملف الشخصي">Profile</a></li>
                            <li><a class="dropdown-item" href="settings.html" data-en="Settings" data-ar="الإعدادات">Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()" data-en="Logout" data-ar="تسجيل الخروج">Logout</a></li>
                        </ul>
                    </div>
                </div>
            </header>
            
            <!-- Dashboard Content -->
            <div class="p-4">
                <!-- Statistics Cards -->
                <div class="row g-4 mb-4">
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card">
                            <div class="stat-label" data-en="Total Sales" data-ar="إجمالي المبيعات">Total Sales</div>
                            <div class="stat-number" id="total-sales">$0</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                <span data-en="+12.5% from last month" data-ar="+12.5% من الشهر الماضي">+12.5% from last month</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card">
                            <div class="stat-label" data-en="Total Orders" data-ar="إجمالي الطلبات">Total Orders</div>
                            <div class="stat-number" id="total-orders">0</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                <span data-en="+8.2% from last month" data-ar="+8.2% من الشهر الماضي">+8.2% from last month</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card">
                            <div class="stat-label" data-en="Total Customers" data-ar="إجمالي العملاء">Total Customers</div>
                            <div class="stat-number" id="total-customers">0</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                <span data-en="+15.3% from last month" data-ar="+15.3% من الشهر الماضي">+15.3% from last month</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card">
                            <div class="stat-label" data-en="Total Products" data-ar="إجمالي المنتجات">Total Products</div>
                            <div class="stat-number" id="total-products">0</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                <span data-en="+5.1% from last month" data-ar="+5.1% من الشهر الماضي">+5.1% from last month</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts Row -->
                <div class="row g-4 mb-4">
                    <div class="col-lg-8">
                        <div class="chart-container">
                            <h5 class="mb-3" data-en="Sales Overview" data-ar="نظرة عامة على المبيعات">Sales Overview</h5>
                            <canvas id="salesChart" height="300"></canvas>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="chart-container">
                            <h5 class="mb-3" data-en="Order Status" data-ar="حالة الطلبات">Order Status</h5>
                            <canvas id="orderStatusChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Orders -->
                <div class="row">
                    <div class="col-12">
                        <div class="recent-orders">
                            <div class="card-header bg-white border-bottom">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0" data-en="Recent Orders" data-ar="الطلبات الأخيرة">Recent Orders</h5>
                                    <a href="orders.html" class="btn btn-sm btn-outline-primary" data-en="View All" data-ar="عرض الكل">View All</a>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th data-en="Order ID" data-ar="رقم الطلب">Order ID</th>
                                                <th data-en="Customer" data-ar="العميل">Customer</th>
                                                <th data-en="Date" data-ar="التاريخ">Date</th>
                                                <th data-en="Status" data-ar="الحالة">Status</th>
                                                <th data-en="Total" data-ar="المجموع">Total</th>
                                                <th data-en="Actions" data-ar="الإجراءات">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="recent-orders-tbody">
                                            <!-- Recent orders will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="../assets/js/language.js"></script>
    
    <script>
        // Check admin authentication
        document.addEventListener('DOMContentLoaded', function() {
            if (!localStorage.getItem('adminLoggedIn')) {
                window.location.href = 'login.html';
                return;
            }
            
            loadDashboardData();
            initializeCharts();
        });
        
        function loadDashboardData() {
            // Load data from localStorage (in real app, this would be from API)
            const orders = JSON.parse(localStorage.getItem('orders')) || [];
            const users = JSON.parse(localStorage.getItem('users')) || [];
            const products = JSON.parse(localStorage.getItem('products')) || [];
            
            // Calculate statistics
            const totalSales = orders.reduce((sum, order) => sum + order.total, 0);
            const totalOrders = orders.length;
            const totalCustomers = users.length;
            const totalProducts = products.length;
            
            // Update statistics
            document.getElementById('total-sales').textContent = `$${totalSales.toFixed(2)}`;
            document.getElementById('total-orders').textContent = totalOrders;
            document.getElementById('total-customers').textContent = totalCustomers;
            document.getElementById('total-products').textContent = totalProducts;
            
            // Load recent orders
            loadRecentOrders(orders.slice(-5).reverse());
        }
        
        function loadRecentOrders(orders) {
            const tbody = document.getElementById('recent-orders-tbody');
            tbody.innerHTML = '';
            
            if (orders.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center py-4 text-muted" data-en="No recent orders" data-ar="لا توجد طلبات حديثة">
                            No recent orders
                        </td>
                    </tr>
                `;
                return;
            }
            
            orders.forEach(order => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>#${order.id}</td>
                    <td>${order.customerName || 'Guest'}</td>
                    <td>${new Date(order.createdAt).toLocaleDateString()}</td>
                    <td>
                        <span class="badge bg-${getStatusColor(order.status)}">${order.status}</span>
                    </td>
                    <td>$${order.total.toFixed(2)}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="viewOrder(${order.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }
        
        function getStatusColor(status) {
            const colors = {
                'pending': 'warning',
                'processing': 'info',
                'shipped': 'primary',
                'delivered': 'success',
                'cancelled': 'danger'
            };
            return colors[status] || 'secondary';
        }
        
        function initializeCharts() {
            // Sales Chart
            const salesCtx = document.getElementById('salesChart').getContext('2d');
            new Chart(salesCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Sales',
                        data: [12000, 19000, 15000, 25000, 22000, 30000],
                        borderColor: '#6366f1',
                        backgroundColor: 'rgba(99, 102, 241, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
            
            // Order Status Chart
            const statusCtx = document.getElementById('orderStatusChart').getContext('2d');
            new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Pending', 'Processing', 'Shipped', 'Delivered'],
                    datasets: [{
                        data: [15, 25, 30, 30],
                        backgroundColor: [
                            '#fbbf24',
                            '#3b82f6',
                            '#6366f1',
                            '#10b981'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
        
        function toggleSidebar() {
            const sidebar = document.getElementById('admin-sidebar');
            sidebar.classList.toggle('show');
        }
        
        function viewOrder(orderId) {
            window.location.href = `order-details.html?id=${orderId}`;
        }
        
        function logout() {
            localStorage.removeItem('adminLoggedIn');
            localStorage.removeItem('adminUser');
            window.location.href = 'login.html';
        }
        
        // Close sidebar on mobile when clicking outside
        document.addEventListener('click', function(e) {
            const sidebar = document.getElementById('admin-sidebar');
            const menuBtn = document.querySelector('.mobile-menu-btn');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(e.target) && 
                !menuBtn.contains(e.target) && 
                sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
            }
        });
    </script>
</body>
</html>

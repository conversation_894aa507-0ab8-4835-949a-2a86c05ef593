// Service Worker for ModernShop
// Provides offline functionality and caching

const CACHE_NAME = 'modernshop-v1';
const STATIC_CACHE = 'modernshop-static-v1';
const DYNAMIC_CACHE = 'modernshop-dynamic-v1';

// Files to cache immediately
const STATIC_FILES = [
    '/',
    '/index.html',
    '/products.html',
    '/cart.html',
    '/assets/css/style.css',
    '/assets/css/rtl.css',
    '/assets/js/app.js',
    '/assets/js/language.js',
    '/assets/js/products.js',
    '/assets/js/demo-data.js',
    '/assets/js/performance.js',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
];

// Install event - cache static files
self.addEventListener('install', event => {
    console.log('Service Worker installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('Caching static files...');
                return cache.addAll(STATIC_FILES);
            })
            .then(() => {
                console.log('Static files cached successfully');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Error caching static files:', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve cached files or fetch from network
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip chrome-extension and other non-http requests
    if (!url.protocol.startsWith('http')) {
        return;
    }
    
    event.respondWith(
        caches.match(request)
            .then(cachedResponse => {
                if (cachedResponse) {
                    console.log('Serving from cache:', request.url);
                    return cachedResponse;
                }
                
                // Not in cache, fetch from network
                return fetch(request)
                    .then(networkResponse => {
                        // Check if we received a valid response
                        if (!networkResponse || networkResponse.status !== 200 || networkResponse.type !== 'basic') {
                            return networkResponse;
                        }
                        
                        // Clone the response
                        const responseToCache = networkResponse.clone();
                        
                        // Determine which cache to use
                        const cacheToUse = isStaticFile(request.url) ? STATIC_CACHE : DYNAMIC_CACHE;
                        
                        // Add to cache
                        caches.open(cacheToUse)
                            .then(cache => {
                                console.log('Caching new resource:', request.url);
                                cache.put(request, responseToCache);
                            });
                        
                        return networkResponse;
                    })
                    .catch(error => {
                        console.error('Fetch failed:', error);
                        
                        // Return offline page for navigation requests
                        if (request.destination === 'document') {
                            return caches.match('/offline.html');
                        }
                        
                        // Return placeholder for images
                        if (request.destination === 'image') {
                            return caches.match('/assets/images/placeholder.jpg');
                        }
                        
                        throw error;
                    });
            })
    );
});

// Background sync for offline actions
self.addEventListener('sync', event => {
    console.log('Background sync triggered:', event.tag);
    
    if (event.tag === 'cart-sync') {
        event.waitUntil(syncCart());
    }
    
    if (event.tag === 'order-sync') {
        event.waitUntil(syncOrders());
    }
});

// Push notifications
self.addEventListener('push', event => {
    console.log('Push notification received:', event);
    
    const options = {
        body: event.data ? event.data.text() : 'New notification from ModernShop',
        icon: '/assets/images/icon-192.png',
        badge: '/assets/images/badge-72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'View',
                icon: '/assets/images/checkmark.png'
            },
            {
                action: 'close',
                title: 'Close',
                icon: '/assets/images/xmark.png'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification('ModernShop', options)
    );
});

// Notification click handler
self.addEventListener('notificationclick', event => {
    console.log('Notification clicked:', event);
    
    event.notification.close();
    
    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

// Helper functions
function isStaticFile(url) {
    const staticExtensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.woff', '.woff2'];
    return staticExtensions.some(ext => url.includes(ext)) || STATIC_FILES.includes(url);
}

async function syncCart() {
    try {
        // Get offline cart data
        const cartData = await getOfflineData('cart');
        
        if (cartData && cartData.length > 0) {
            // Sync with server
            const response = await fetch('/api/cart/sync', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(cartData)
            });
            
            if (response.ok) {
                // Clear offline data
                await clearOfflineData('cart');
                console.log('Cart synced successfully');
            }
        }
    } catch (error) {
        console.error('Cart sync failed:', error);
    }
}

async function syncOrders() {
    try {
        // Get offline order data
        const orderData = await getOfflineData('orders');
        
        if (orderData && orderData.length > 0) {
            // Sync with server
            const response = await fetch('/api/orders/sync', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(orderData)
            });
            
            if (response.ok) {
                // Clear offline data
                await clearOfflineData('orders');
                console.log('Orders synced successfully');
            }
        }
    } catch (error) {
        console.error('Orders sync failed:', error);
    }
}

async function getOfflineData(key) {
    try {
        const cache = await caches.open(DYNAMIC_CACHE);
        const response = await cache.match(`/offline-data/${key}`);
        
        if (response) {
            return await response.json();
        }
        
        return null;
    } catch (error) {
        console.error('Error getting offline data:', error);
        return null;
    }
}

async function clearOfflineData(key) {
    try {
        const cache = await caches.open(DYNAMIC_CACHE);
        await cache.delete(`/offline-data/${key}`);
    } catch (error) {
        console.error('Error clearing offline data:', error);
    }
}

// Cache management
async function cleanupCaches() {
    const cacheNames = await caches.keys();
    
    for (const cacheName of cacheNames) {
        if (cacheName === DYNAMIC_CACHE) {
            const cache = await caches.open(cacheName);
            const requests = await cache.keys();
            
            // Remove old entries (keep only last 50)
            if (requests.length > 50) {
                const toDelete = requests.slice(0, requests.length - 50);
                await Promise.all(toDelete.map(request => cache.delete(request)));
            }
        }
    }
}

// Periodic cleanup
setInterval(cleanupCaches, 60000); // Every minute

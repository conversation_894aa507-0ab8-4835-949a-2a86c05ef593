// Modern E-commerce Application - Main JavaScript File

// Application State Management
class ECommerceApp {
    constructor() {
        this.cart = JSON.parse(localStorage.getItem('cart')) || [];
        this.users = JSON.parse(localStorage.getItem('users')) || [];
        this.products = JSON.parse(localStorage.getItem('products')) || this.getDefaultProducts();
        this.orders = JSON.parse(localStorage.getItem('orders')) || [];
        this.currentUser = JSON.parse(localStorage.getItem('currentUser')) || null;
        this.currentLanguage = localStorage.getItem('language') || 'en';
        
        this.init();
    }
    
    init() {
        this.updateCartCount();
        this.loadFeaturedProducts();
        this.setupEventListeners();
        this.setLanguage(this.currentLanguage);
    }
    
    // Product Management
    getDefaultProducts() {
        return [
            {
                id: 1,
                name: { en: "Premium Laptop", ar: "لابتوب مميز" },
                description: { 
                    en: "High-performance laptop for professionals", 
                    ar: "لابتوب عالي الأداء للمحترفين" 
                },
                price: 1299.99,
                image: "assets/images/laptop.jpg",
                category: "electronics",
                rating: 4.8,
                reviews: 124,
                inStock: true,
                featured: true
            },
            {
                id: 2,
                name: { en: "Wireless Headphones", ar: "سماعات لاسلكية" },
                description: { 
                    en: "Premium wireless headphones with noise cancellation", 
                    ar: "سماعات لاسلكية مميزة مع إلغاء الضوضاء" 
                },
                price: 299.99,
                image: "assets/images/headphones.jpg",
                category: "electronics",
                rating: 4.6,
                reviews: 89,
                inStock: true,
                featured: true
            },
            {
                id: 3,
                name: { en: "Smart Watch", ar: "ساعة ذكية" },
                description: { 
                    en: "Advanced smartwatch with health monitoring", 
                    ar: "ساعة ذكية متقدمة مع مراقبة الصحة" 
                },
                price: 399.99,
                image: "assets/images/smartwatch.jpg",
                category: "electronics",
                rating: 4.7,
                reviews: 156,
                inStock: true,
                featured: true
            },
            {
                id: 4,
                name: { en: "Professional Camera", ar: "كاميرا احترافية" },
                description: { 
                    en: "High-resolution camera for photography enthusiasts", 
                    ar: "كاميرا عالية الدقة لعشاق التصوير" 
                },
                price: 899.99,
                image: "assets/images/camera.jpg",
                category: "electronics",
                rating: 4.9,
                reviews: 78,
                inStock: true,
                featured: true
            }
        ];
    }
    
    getProducts() {
        return this.products;
    }
    
    getProduct(id) {
        return this.products.find(product => product.id === parseInt(id));
    }
    
    getFeaturedProducts() {
        return this.products.filter(product => product.featured);
    }
    
    // Cart Management
    addToCart(productId, quantity = 1) {
        const product = this.getProduct(productId);
        if (!product) return false;
        
        const existingItem = this.cart.find(item => item.productId === productId);
        
        if (existingItem) {
            existingItem.quantity += quantity;
        } else {
            this.cart.push({
                productId: productId,
                quantity: quantity,
                addedAt: new Date().toISOString()
            });
        }
        
        this.saveCart();
        this.updateCartCount();
        this.showNotification(
            this.currentLanguage === 'ar' ? 'تم إضافة المنتج إلى السلة' : 'Product added to cart',
            'success'
        );
        return true;
    }
    
    removeFromCart(productId) {
        this.cart = this.cart.filter(item => item.productId !== productId);
        this.saveCart();
        this.updateCartCount();
    }
    
    updateCartQuantity(productId, quantity) {
        const item = this.cart.find(item => item.productId === productId);
        if (item) {
            if (quantity <= 0) {
                this.removeFromCart(productId);
            } else {
                item.quantity = quantity;
                this.saveCart();
                this.updateCartCount();
            }
        }
    }
    
    getCartItems() {
        return this.cart.map(item => {
            const product = this.getProduct(item.productId);
            return {
                ...item,
                product: product
            };
        });
    }
    
    getCartTotal() {
        return this.getCartItems().reduce((total, item) => {
            return total + (item.product.price * item.quantity);
        }, 0);
    }
    
    clearCart() {
        this.cart = [];
        this.saveCart();
        this.updateCartCount();
    }
    
    saveCart() {
        localStorage.setItem('cart', JSON.stringify(this.cart));
    }
    
    updateCartCount() {
        const count = this.cart.reduce((total, item) => total + item.quantity, 0);
        const cartCountElement = document.getElementById('cart-count');
        if (cartCountElement) {
            cartCountElement.textContent = count;
        }
    }
    
    // User Management
    registerUser(userData) {
        const existingUser = this.users.find(user => user.email === userData.email);
        if (existingUser) {
            return { success: false, message: 'User already exists' };
        }
        
        const newUser = {
            id: Date.now(),
            ...userData,
            createdAt: new Date().toISOString(),
            isAdmin: false
        };
        
        this.users.push(newUser);
        localStorage.setItem('users', JSON.stringify(this.users));
        
        return { success: true, user: newUser };
    }
    
    loginUser(email, password) {
        const user = this.users.find(u => u.email === email && u.password === password);
        if (user) {
            this.currentUser = user;
            localStorage.setItem('currentUser', JSON.stringify(user));
            return { success: true, user: user };
        }
        return { success: false, message: 'Invalid credentials' };
    }
    
    logoutUser() {
        this.currentUser = null;
        localStorage.removeItem('currentUser');
    }
    
    isLoggedIn() {
        return this.currentUser !== null;
    }
    
    isAdmin() {
        return this.currentUser && this.currentUser.isAdmin;
    }
    
    // Order Management
    createOrder(orderData) {
        const order = {
            id: Date.now(),
            userId: this.currentUser ? this.currentUser.id : null,
            items: this.getCartItems(),
            total: this.getCartTotal(),
            status: 'pending',
            createdAt: new Date().toISOString(),
            ...orderData
        };
        
        this.orders.push(order);
        localStorage.setItem('orders', JSON.stringify(this.orders));
        this.clearCart();
        
        return order;
    }
    
    getOrders() {
        return this.orders;
    }
    
    getUserOrders(userId) {
        return this.orders.filter(order => order.userId === userId);
    }
    
    updateOrderStatus(orderId, status) {
        const order = this.orders.find(o => o.id === orderId);
        if (order) {
            order.status = status;
            order.updatedAt = new Date().toISOString();
            localStorage.setItem('orders', JSON.stringify(this.orders));
            return true;
        }
        return false;
    }
    
    // Utility Functions
    loadFeaturedProducts() {
        const container = document.getElementById('featured-products');
        if (!container) return;
        
        const featuredProducts = this.getFeaturedProducts();
        container.innerHTML = '';
        
        featuredProducts.forEach(product => {
            const productCard = this.createProductCard(product);
            container.appendChild(productCard);
        });
    }
    
    createProductCard(product) {
        const card = document.createElement('div');
        card.className = 'col-lg-3 col-md-6 mb-4';
        
        const productName = product.name[this.currentLanguage] || product.name.en;
        const productDesc = product.description[this.currentLanguage] || product.description.en;
        
        card.innerHTML = `
            <div class="product-card h-100">
                <div class="product-image">
                    <img src="${product.image}" alt="${productName}" class="img-fluid">
                    <div class="product-overlay">
                        <button class="btn btn-light btn-sm" onclick="app.addToCart(${product.id})">
                            <i class="fas fa-shopping-cart"></i>
                            <span data-en="Add to Cart" data-ar="أضف للسلة">Add to Cart</span>
                        </button>
                    </div>
                </div>
                <div class="product-info">
                    <h5 class="product-title">${productName}</h5>
                    <p class="text-muted">${productDesc}</p>
                    <div class="product-rating">
                        <div class="stars">
                            ${'★'.repeat(Math.floor(product.rating))}${'☆'.repeat(5 - Math.floor(product.rating))}
                        </div>
                        <span class="text-muted">(${product.reviews})</span>
                    </div>
                    <div class="product-price">$${product.price}</div>
                    <button class="btn btn-primary w-100" onclick="app.addToCart(${product.id})">
                        <i class="fas fa-cart-plus"></i>
                        <span data-en="Add to Cart" data-ar="أضف للسلة">Add to Cart</span>
                    </button>
                </div>
            </div>
        `;
        
        return card;
    }
    
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }
    
    setupEventListeners() {
        // Add any global event listeners here
        document.addEventListener('DOMContentLoaded', () => {
            this.updateCartCount();
        });
    }
    
    // Language Management
    setLanguage(lang) {
        this.currentLanguage = lang;
        localStorage.setItem('language', lang);
        
        const html = document.documentElement;
        const body = document.body;
        
        if (lang === 'ar') {
            html.setAttribute('lang', 'ar');
            html.setAttribute('dir', 'rtl');
            body.setAttribute('dir', 'rtl');
        } else {
            html.setAttribute('lang', 'en');
            html.setAttribute('dir', 'ltr');
            body.setAttribute('dir', 'ltr');
        }
        
        this.updateLanguageContent();
    }
    
    updateLanguageContent() {
        const elements = document.querySelectorAll('[data-en][data-ar]');
        elements.forEach(element => {
            const text = element.getAttribute(`data-${this.currentLanguage}`);
            if (text) {
                element.textContent = text;
            }
        });
        
        // Update placeholders
        const placeholderElements = document.querySelectorAll('[data-en-placeholder][data-ar-placeholder]');
        placeholderElements.forEach(element => {
            const placeholder = element.getAttribute(`data-${this.currentLanguage}-placeholder`);
            if (placeholder) {
                element.setAttribute('placeholder', placeholder);
            }
        });
        
        // Update language switcher text
        const langText = document.getElementById('lang-text');
        if (langText) {
            langText.textContent = this.currentLanguage === 'en' ? 'العربية' : 'English';
        }
    }
}

// Initialize the application
const app = new ECommerceApp();

// Global functions
function toggleLanguage() {
    const newLang = app.currentLanguage === 'en' ? 'ar' : 'en';
    app.setLanguage(newLang);
    
    // Reload featured products with new language
    app.loadFeaturedProducts();
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ECommerceApp;
}

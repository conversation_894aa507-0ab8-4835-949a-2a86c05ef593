/* Modern E-commerce Website Styles */

:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #f8fafc;
    --accent-color: #10b981;
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --border-radius: 0.75rem;
    --transition: all 0.3s ease;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: #ffffff;
}

/* Language Switcher */
.language-switcher {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
}

.language-switcher .btn {
    border-radius: 50px;
    padding: 8px 16px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition);
}

.language-switcher .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Navigation Styles */
.navbar {
    padding: 1rem 0;
    transition: var(--transition);
}

.navbar-brand {
    font-size: 1.5rem;
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: var(--text-dark) !important;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    background-color: var(--secondary-color);
    color: var(--primary-color) !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
}

.min-vh-75 {
    min-height: 75vh;
}

.hero-image img {
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.hero-image img:hover {
    transform: scale(1.05);
}

/* Feature Cards */
.feature-card {
    background: white;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.feature-icon {
    transition: var(--transition);
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
}

/* Product Cards */
.product-card {
    background: white;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    overflow: hidden;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.product-image {
    height: 250px;
    overflow: hidden;
    position: relative;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-info {
    padding: 1.5rem;
}

.product-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.product-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.stars {
    color: #fbbf24;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.btn-success {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.btn-success:hover {
    background-color: #059669;
    border-color: #059669;
    transform: translateY(-2px);
}

/* Forms */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
}

/* Cart Badge */
.badge {
    font-size: 0.75rem;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-50px); }
    to { opacity: 1; transform: translateX(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(50px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 3rem 0;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .language-switcher {
        top: 10px;
        right: 10px;
    }
    
    .feature-card,
    .product-card {
        margin-bottom: 1.5rem;
    }
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-light {
    background-color: var(--secondary-color) !important;
}

.shadow-custom {
    box-shadow: var(--shadow-lg);
}

.border-radius-custom {
    border-radius: var(--border-radius);
}

/* Footer */
footer {
    background-color: #1f2937 !important;
}

footer a {
    text-decoration: none;
    transition: var(--transition);
}

footer a:hover {
    color: var(--primary-color) !important;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    transition: var(--transition);
}

.social-links a:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
}

/* Admin Panel Styles */
.admin-sidebar {
    background-color: #1f2937;
    min-height: 100vh;
    padding: 2rem 0;
}

.admin-sidebar .nav-link {
    color: #d1d5db;
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    transition: var(--transition);
}

.admin-sidebar .nav-link:hover,
.admin-sidebar .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.admin-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.admin-card:hover {
    box-shadow: var(--shadow-md);
}

.stat-card {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
}

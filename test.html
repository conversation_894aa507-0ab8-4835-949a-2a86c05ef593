<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page - ModernShop</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/rtl.css">
    
    <style>
        .test-section {
            padding: 2rem 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .test-result {
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 0.5rem 0;
        }
        
        .test-pass {
            background-color: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        
        .test-fail {
            background-color: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        
        .test-info {
            background-color: #dbeafe;
            border: 1px solid #3b82f6;
            color: #1e40af;
        }
        
        .performance-metrics {
            background: #f8fafc;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .metric-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .metric-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <!-- Language Switcher -->
    <div class="language-switcher">
        <button class="btn btn-sm btn-outline-primary" onclick="toggleLanguage()">
            <i class="fas fa-globe"></i>
            <span id="lang-text">العربية</span>
        </button>
    </div>

    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4" data-en="ModernShop Test Suite" data-ar="مجموعة اختبارات متجر حديث">
                    ModernShop Test Suite
                </h1>
                <p class="lead" data-en="Comprehensive testing of all website functionality" data-ar="اختبار شامل لجميع وظائف الموقع">
                    Comprehensive testing of all website functionality
                </p>
            </div>
        </div>
        
        <!-- Language Testing -->
        <div class="test-section">
            <h3 data-en="Language & RTL Testing" data-ar="اختبار اللغة والاتجاه">Language & RTL Testing</h3>
            <div id="language-tests"></div>
            
            <div class="mt-3">
                <button class="btn btn-primary" onclick="testLanguageSwitching()" data-en="Test Language Switching" data-ar="اختبار تبديل اللغة">
                    Test Language Switching
                </button>
            </div>
        </div>
        
        <!-- Product Management Testing -->
        <div class="test-section">
            <h3 data-en="Product Management Testing" data-ar="اختبار إدارة المنتجات">Product Management Testing</h3>
            <div id="product-tests"></div>
            
            <div class="mt-3">
                <button class="btn btn-primary" onclick="testProductManagement()" data-en="Test Product Functions" data-ar="اختبار وظائف المنتجات">
                    Test Product Functions
                </button>
            </div>
        </div>
        
        <!-- Cart Testing -->
        <div class="test-section">
            <h3 data-en="Shopping Cart Testing" data-ar="اختبار سلة التسوق">Shopping Cart Testing</h3>
            <div id="cart-tests"></div>
            
            <div class="mt-3">
                <button class="btn btn-primary" onclick="testCartFunctionality()" data-en="Test Cart Functions" data-ar="اختبار وظائف السلة">
                    Test Cart Functions
                </button>
            </div>
        </div>
        
        <!-- Performance Testing -->
        <div class="test-section">
            <h3 data-en="Performance Testing" data-ar="اختبار الأداء">Performance Testing</h3>
            <div id="performance-tests"></div>
            
            <div class="performance-metrics" id="performance-metrics">
                <!-- Performance metrics will be displayed here -->
            </div>
            
            <div class="mt-3">
                <button class="btn btn-primary" onclick="testPerformance()" data-en="Test Performance" data-ar="اختبار الأداء">
                    Test Performance
                </button>
            </div>
        </div>
        
        <!-- Responsive Design Testing -->
        <div class="test-section">
            <h3 data-en="Responsive Design Testing" data-ar="اختبار التصميم المتجاوب">Responsive Design Testing</h3>
            <div id="responsive-tests"></div>
            
            <div class="mt-3">
                <button class="btn btn-primary" onclick="testResponsiveDesign()" data-en="Test Responsive Design" data-ar="اختبار التصميم المتجاوب">
                    Test Responsive Design
                </button>
            </div>
        </div>
        
        <!-- Admin Dashboard Testing -->
        <div class="test-section">
            <h3 data-en="Admin Dashboard Testing" data-ar="اختبار لوحة الإدارة">Admin Dashboard Testing</h3>
            <div id="admin-tests"></div>
            
            <div class="mt-3">
                <button class="btn btn-primary" onclick="testAdminFunctionality()" data-en="Test Admin Functions" data-ar="اختبار وظائف الإدارة">
                    Test Admin Functions
                </button>
                <a href="admin/login.html" class="btn btn-outline-secondary ms-2" data-en="Go to Admin" data-ar="الذهاب للإدارة">
                    Go to Admin
                </a>
            </div>
        </div>
        
        <!-- Overall Results -->
        <div class="test-section">
            <h3 data-en="Overall Test Results" data-ar="النتائج الإجمالية للاختبار">Overall Test Results</h3>
            <div id="overall-results"></div>
            
            <div class="mt-3">
                <button class="btn btn-success" onclick="runAllTests()" data-en="Run All Tests" data-ar="تشغيل جميع الاختبارات">
                    Run All Tests
                </button>
                <button class="btn btn-outline-primary ms-2" onclick="exportResults()" data-en="Export Results" data-ar="تصدير النتائج">
                    Export Results
                </button>
            </div>
        </div>
        
        <!-- Navigation Links -->
        <div class="test-section">
            <h3 data-en="Quick Navigation" data-ar="التنقل السريع">Quick Navigation</h3>
            <div class="row g-3">
                <div class="col-md-3">
                    <a href="index.html" class="btn btn-outline-primary w-100" data-en="Homepage" data-ar="الصفحة الرئيسية">Homepage</a>
                </div>
                <div class="col-md-3">
                    <a href="products.html" class="btn btn-outline-primary w-100" data-en="Products" data-ar="المنتجات">Products</a>
                </div>
                <div class="col-md-3">
                    <a href="cart.html" class="btn btn-outline-primary w-100" data-en="Cart" data-ar="السلة">Cart</a>
                </div>
                <div class="col-md-3">
                    <a href="admin/dashboard.html" class="btn btn-outline-primary w-100" data-en="Admin" data-ar="الإدارة">Admin</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="assets/js/performance.js"></script>
    <script src="assets/js/demo-data.js"></script>
    <script src="assets/js/app.js"></script>
    <script src="assets/js/language.js"></script>
    <script src="assets/js/products.js"></script>
    
    <script>
        // Test Suite Implementation
        let testResults = {
            language: [],
            products: [],
            cart: [],
            performance: [],
            responsive: [],
            admin: []
        };
        
        function addTestResult(category, test, passed, message) {
            const result = { test, passed, message, timestamp: new Date() };
            testResults[category].push(result);
            
            const container = document.getElementById(`${category}-tests`);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${passed ? 'test-pass' : 'test-fail'}`;
            resultDiv.innerHTML = `
                <strong>${test}:</strong> ${passed ? '✓ PASS' : '✗ FAIL'} - ${message}
            `;
            container.appendChild(resultDiv);
        }
        
        function testLanguageSwitching() {
            const container = document.getElementById('language-tests');
            container.innerHTML = '';
            
            try {
                // Test language manager exists
                const langExists = typeof languageManager !== 'undefined';
                addTestResult('language', 'Language Manager', langExists, 
                    langExists ? 'Language manager initialized' : 'Language manager not found');
                
                // Test current language
                const currentLang = getCurrentLanguage();
                addTestResult('language', 'Get Current Language', !!currentLang, 
                    `Current language: ${currentLang}`);
                
                // Test language switching
                const originalLang = currentLang;
                toggleLanguage();
                const newLang = getCurrentLanguage();
                const switched = originalLang !== newLang;
                addTestResult('language', 'Language Switching', switched, 
                    switched ? `Switched from ${originalLang} to ${newLang}` : 'Language did not switch');
                
                // Test RTL detection
                const isRTLMode = isRTL();
                addTestResult('language', 'RTL Detection', typeof isRTLMode === 'boolean', 
                    `RTL mode: ${isRTLMode}`);
                
                // Test translation function
                const translation = getTranslation('home');
                addTestResult('language', 'Translation Function', !!translation, 
                    `Translation for 'home': ${translation}`);
                
            } catch (error) {
                addTestResult('language', 'Language Testing', false, `Error: ${error.message}`);
            }
        }
        
        function testProductManagement() {
            const container = document.getElementById('product-tests');
            container.innerHTML = '';
            
            try {
                // Test product manager exists
                const pmExists = typeof productManager !== 'undefined';
                addTestResult('products', 'Product Manager', pmExists, 
                    pmExists ? 'Product manager initialized' : 'Product manager not found');
                
                // Test get all products
                const products = productManager.getAllProducts();
                addTestResult('products', 'Get All Products', Array.isArray(products), 
                    `Found ${products.length} products`);
                
                // Test get single product
                const product = productManager.getProduct(1);
                addTestResult('products', 'Get Single Product', !!product, 
                    product ? `Product: ${product.name.en}` : 'Product not found');
                
                // Test featured products
                const featured = productManager.getFeaturedProducts();
                addTestResult('products', 'Get Featured Products', Array.isArray(featured), 
                    `Found ${featured.length} featured products`);
                
                // Test filtering
                productManager.setFilter('category', 'electronics');
                const filtered = productManager.getFilteredProducts();
                addTestResult('products', 'Product Filtering', Array.isArray(filtered), 
                    `Filtered to ${filtered.length} electronics products`);
                
                // Reset filters
                productManager.clearFilters();
                
            } catch (error) {
                addTestResult('products', 'Product Management', false, `Error: ${error.message}`);
            }
        }
        
        function testCartFunctionality() {
            const container = document.getElementById('cart-tests');
            container.innerHTML = '';
            
            try {
                // Test app exists
                const appExists = typeof app !== 'undefined';
                addTestResult('cart', 'App Instance', appExists, 
                    appExists ? 'App instance found' : 'App instance not found');
                
                // Clear cart first
                app.clearCart();
                
                // Test add to cart
                const added = app.addToCart(1, 2);
                addTestResult('cart', 'Add to Cart', added, 
                    added ? 'Product added successfully' : 'Failed to add product');
                
                // Test get cart items
                const cartItems = app.getCartItems();
                addTestResult('cart', 'Get Cart Items', Array.isArray(cartItems), 
                    `Cart has ${cartItems.length} items`);
                
                // Test cart total
                const total = app.getCartTotal();
                addTestResult('cart', 'Calculate Cart Total', typeof total === 'number', 
                    `Cart total: $${total.toFixed(2)}`);
                
                // Test update quantity
                app.updateCartQuantity(1, 3);
                const updatedItems = app.getCartItems();
                const updatedQuantity = updatedItems.length > 0 ? updatedItems[0].quantity : 0;
                addTestResult('cart', 'Update Quantity', updatedQuantity === 3, 
                    `Updated quantity to ${updatedQuantity}`);
                
                // Test remove from cart
                app.removeFromCart(1);
                const afterRemoval = app.getCartItems();
                addTestResult('cart', 'Remove from Cart', afterRemoval.length === 0, 
                    `Cart items after removal: ${afterRemoval.length}`);
                
            } catch (error) {
                addTestResult('cart', 'Cart Functionality', false, `Error: ${error.message}`);
            }
        }
        
        function testPerformance() {
            const container = document.getElementById('performance-tests');
            const metricsContainer = document.getElementById('performance-metrics');
            container.innerHTML = '';
            metricsContainer.innerHTML = '';
            
            try {
                // Test performance optimizer exists
                const perfExists = typeof performanceOptimizer !== 'undefined';
                addTestResult('performance', 'Performance Optimizer', perfExists, 
                    perfExists ? 'Performance optimizer loaded' : 'Performance optimizer not found');
                
                // Test performance metrics
                if (perfExists) {
                    const metrics = performanceOptimizer.getMetrics();
                    addTestResult('performance', 'Get Metrics', !!metrics, 
                        'Performance metrics retrieved');
                    
                    // Display metrics
                    metricsContainer.innerHTML = `
                        <h5>Performance Metrics</h5>
                        <div class="metric-item">
                            <span>Load Time:</span>
                            <span>${metrics.loadTime ? metrics.loadTime.toFixed(2) + 'ms' : 'N/A'}</span>
                        </div>
                        <div class="metric-item">
                            <span>Image Cache Size:</span>
                            <span>${metrics.cacheSize.images} items</span>
                        </div>
                        <div class="metric-item">
                            <span>API Cache Size:</span>
                            <span>${metrics.cacheSize.api} items</span>
                        </div>
                        ${metrics.memoryUsage ? `
                        <div class="metric-item">
                            <span>Memory Usage:</span>
                            <span>${(metrics.memoryUsage.used / 1024 / 1024).toFixed(2)} MB</span>
                        </div>
                        ` : ''}
                    `;
                }
                
                // Test service worker
                const swSupported = 'serviceWorker' in navigator;
                addTestResult('performance', 'Service Worker Support', swSupported, 
                    swSupported ? 'Service Worker supported' : 'Service Worker not supported');
                
                // Test local storage
                const lsSupported = typeof Storage !== 'undefined';
                addTestResult('performance', 'Local Storage Support', lsSupported, 
                    lsSupported ? 'Local Storage supported' : 'Local Storage not supported');
                
            } catch (error) {
                addTestResult('performance', 'Performance Testing', false, `Error: ${error.message}`);
            }
        }
        
        function testResponsiveDesign() {
            const container = document.getElementById('responsive-tests');
            container.innerHTML = '';
            
            try {
                // Test viewport meta tag
                const viewport = document.querySelector('meta[name="viewport"]');
                addTestResult('responsive', 'Viewport Meta Tag', !!viewport, 
                    viewport ? 'Viewport meta tag found' : 'Viewport meta tag missing');
                
                // Test Bootstrap classes
                const bootstrapElements = document.querySelectorAll('.container, .row, .col');
                addTestResult('responsive', 'Bootstrap Grid Classes', bootstrapElements.length > 0, 
                    `Found ${bootstrapElements.length} Bootstrap grid elements`);
                
                // Test responsive images
                const responsiveImages = document.querySelectorAll('.img-fluid, .img-responsive');
                addTestResult('responsive', 'Responsive Images', responsiveImages.length > 0, 
                    `Found ${responsiveImages.length} responsive images`);
                
                // Test media queries
                const stylesheets = Array.from(document.styleSheets);
                let mediaQueriesFound = false;
                try {
                    stylesheets.forEach(sheet => {
                        if (sheet.cssRules) {
                            Array.from(sheet.cssRules).forEach(rule => {
                                if (rule.type === CSSRule.MEDIA_RULE) {
                                    mediaQueriesFound = true;
                                }
                            });
                        }
                    });
                } catch (e) {
                    // Cross-origin stylesheets can't be accessed
                }
                addTestResult('responsive', 'Media Queries', mediaQueriesFound, 
                    mediaQueriesFound ? 'Media queries detected' : 'No media queries found');
                
                // Test current screen size
                const screenSize = `${window.innerWidth}x${window.innerHeight}`;
                const isMobile = window.innerWidth < 768;
                const isTablet = window.innerWidth >= 768 && window.innerWidth < 1024;
                const isDesktop = window.innerWidth >= 1024;
                
                addTestResult('responsive', 'Screen Size Detection', true, 
                    `Current: ${screenSize} (${isMobile ? 'Mobile' : isTablet ? 'Tablet' : 'Desktop'})`);
                
            } catch (error) {
                addTestResult('responsive', 'Responsive Design', false, `Error: ${error.message}`);
            }
        }
        
        function testAdminFunctionality() {
            const container = document.getElementById('admin-tests');
            container.innerHTML = '';
            
            try {
                // Test admin login status
                const adminLoggedIn = localStorage.getItem('adminLoggedIn');
                addTestResult('admin', 'Admin Login Status', !!adminLoggedIn, 
                    adminLoggedIn ? 'Admin is logged in' : 'Admin not logged in');
                
                // Test demo data
                const users = JSON.parse(localStorage.getItem('users')) || [];
                const adminUser = users.find(user => user.isAdmin);
                addTestResult('admin', 'Admin User Exists', !!adminUser, 
                    adminUser ? `Admin user: ${adminUser.email}` : 'No admin user found');
                
                // Test orders data
                const orders = JSON.parse(localStorage.getItem('orders')) || [];
                addTestResult('admin', 'Orders Data', orders.length > 0, 
                    `Found ${orders.length} orders`);
                
                // Test demo data generator
                const demoExists = typeof demoDataGenerator !== 'undefined';
                addTestResult('admin', 'Demo Data Generator', demoExists, 
                    demoExists ? 'Demo data generator available' : 'Demo data generator not found');
                
                // Test statistics calculation
                if (demoExists) {
                    const stats = demoDataGenerator.getDemoStats();
                    addTestResult('admin', 'Statistics Calculation', !!stats, 
                        `Total sales: $${stats.totalSales.toFixed(2)}`);
                }
                
            } catch (error) {
                addTestResult('admin', 'Admin Functionality', false, `Error: ${error.message}`);
            }
        }
        
        function runAllTests() {
            // Clear previous results
            testResults = {
                language: [],
                products: [],
                cart: [],
                performance: [],
                responsive: [],
                admin: []
            };
            
            // Run all tests
            testLanguageSwitching();
            testProductManagement();
            testCartFunctionality();
            testPerformance();
            testResponsiveDesign();
            testAdminFunctionality();
            
            // Show overall results
            setTimeout(showOverallResults, 1000);
        }
        
        function showOverallResults() {
            const container = document.getElementById('overall-results');
            container.innerHTML = '';
            
            let totalTests = 0;
            let passedTests = 0;
            
            Object.keys(testResults).forEach(category => {
                const categoryResults = testResults[category];
                const categoryPassed = categoryResults.filter(r => r.passed).length;
                totalTests += categoryResults.length;
                passedTests += categoryPassed;
                
                const resultDiv = document.createElement('div');
                resultDiv.className = 'test-result test-info';
                resultDiv.innerHTML = `
                    <strong>${category.charAt(0).toUpperCase() + category.slice(1)}:</strong> 
                    ${categoryPassed}/${categoryResults.length} tests passed
                `;
                container.appendChild(resultDiv);
            });
            
            const overallDiv = document.createElement('div');
            overallDiv.className = `test-result ${passedTests === totalTests ? 'test-pass' : 'test-fail'}`;
            overallDiv.innerHTML = `
                <strong>Overall Results:</strong> ${passedTests}/${totalTests} tests passed 
                (${((passedTests / totalTests) * 100).toFixed(1)}%)
            `;
            container.appendChild(overallDiv);
        }
        
        function exportResults() {
            const results = {
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                screenSize: `${window.innerWidth}x${window.innerHeight}`,
                language: getCurrentLanguage(),
                results: testResults
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `modernshop-test-results-${Date.now()}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        // Auto-run basic tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testLanguageSwitching();
                testPerformance();
            }, 1000);
        });
    </script>
</body>
</html>
